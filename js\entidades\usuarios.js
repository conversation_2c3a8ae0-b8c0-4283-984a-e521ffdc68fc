/**
 * Manejador específico para la entidad de usuarios
 */
class UsuariosEntityHandler {
    /**
     * Obtiene la configuración específica para la entidad de usuarios
     * @param {boolean} skipTitleCustomization - Si es true, no personaliza el título (para evitar recursión)
     * @returns {Object} - Configuración de la entidad
     */
    getConfig(skipTitleCustomization = false) {
        // Título por defecto
        let title = 'Usuarios';

        // Personalizar el título solo si no se está evitando la recursión
        if (!skipTitleCustomization) {
            // Verificar si estamos mostrando usuarios de una empresa específica
            const entityManager = app.entityManagers.find(em => em.entityType === 'usuarios');
            const specificCompanyId = entityManager && entityManager.params && entityManager.params.companyId;

            // Si hay un ID de empresa específico, buscar el nombre de la empresa
            if (specificCompanyId) {
                try {
                    // Buscar la empresa directamente en localStorage
                    const companyKey = `empresa_${specificCompanyId}`;
                    const companyData = localStorage.getItem(companyKey);
                    if (companyData) {
                        const company = JSON.parse(companyData);
                        if (company && company.info && company.info.nombre) {
                            title = `Usuarios de ${company.info.nombre}`;
                        }
                    }
                } catch (error) {
                    console.error('Error al obtener nombre de empresa:', error);
                }
            }
        }

        return {
            title: title,
            fields: [{
                    name: 'id',
                    label: 'ID',
                    type: 'number',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'login',
                    label: 'Login',
                    type: 'text',
                    sortable: true,
                    visible: false,
                    required: true
                },
                {
                    name: 'nombre',
                    label: 'Nombre',
                    type: 'text',
                    sortable: true,
                    visible: true,
                    required: true
                },
                {
                    name: 'clave',
                    label: 'Contraseña',
                    type: 'password', // para que no sea visible en formulario de edición ni en listados
                    sortable: false,
                    visible: false, // No se muestra en la tabla pero sí en el formulario
                    required: false // No es obligatorio para permitir edición sin cambiar clave
                },
                {
                    name: 'email',
                    label: 'Email',
                    type: 'email',
                    sortable: true,
                    visible: true,
                    validate: (value) => !validateEmail(value) ? 'Email no válido' : null
                },
                {
                    name: 'tipo',
                    label: 'Perfil',
                    type: 'select',
                    sortable: true,
                    visible: true,
                    required: true,
                    options: [{
                            value: 'admin',
                            label: 'Administrador'
                        },
                        {
                            value: 'editor',
                            label: 'Editar Tableros'
                        },
                        {
                            value: 'viewer',
                            label: 'Visor Tableros'
                        }
                    ]
                },
                {
                    name: 'empresaId',
                    label: 'Empresa',
                    type: 'select',
                    sortable: true,
                    visible: false,
                    required: true,
                    editable: false, // No editable en el formulario
                    options: []
                }
            ],
            actions: ['edit', 'delete']
        };
    }

    /**
     * Obtiene las opciones de empresas para el selector
     * @returns {Array} - Array de objetos con value y label
     */
    getCompanyOptions() {
        // Obtener las empresas directamente de la base de datos
        try {
            // Obtener todas las empresas de la base de datos de forma síncrona
            const allCompanies = Object.values(localStorage)
                .filter(item => item.startsWith('{') && item.includes('"info"'))
                .map(item => {
                    try {
                        const data = JSON.parse(item);
                        return data.info;
                    } catch (e) {
                        return null;
                    }
                })
                .filter(company => company && company.id);

            // Filtrar según permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            if (currentUser.tipo === 'admin' && currentCompanyId === 1) {
                // Administradores de empresa 1 pueden ver todas las empresas
                return allCompanies.map(company => ({
                    value: company.id,
                    label: company.nombre
                }));
            } else {
                // Los demás solo pueden ver su empresa
                const filteredCompanies = allCompanies.filter(company =>
                    company.id === currentCompanyId);
                return filteredCompanies.map(company => ({
                    value: company.id,
                    label: company.nombre
                }));
            }
        } catch (error) {
            console.error('Error al obtener opciones de empresas:', error);
            return [];
        }
    }

    /**
     * Carga las empresas disponibles
     * @returns {Promise<Array>} - Promesa que se resuelve con la lista de empresas
     */
    loadCompanies() {
        return new Promise((resolve, reject) => {
            dbManager.getAllCompanies()
                .then(companies => {
                    // Filtrar empresas según permisos
                    const currentUser = authManager.user;
                    const currentCompanyId = authManager.companyId;

                    if (currentUser.tipo === 'admin' && currentCompanyId === 1) {
                        // Administradores de empresa 1 pueden ver todas las empresas
                        resolve(companies);
                    } else {
                        // Los demás solo pueden ver su empresa
                        const filteredCompanies = companies.filter(company =>
                            company.id === currentCompanyId);
                        resolve(filteredCompanies);
                    }
                })
                .catch(error => {
                    reject(error);
                });
        });
    }

    /**
     * Obtiene las opciones de menú específicas para las filas de la tabla de usuarios
     * @param {Object} entity - La entidad (usuario) sobre la que se mostrarán las opciones
     * @returns {Array} - Opciones de menú para la fila
     */
    getRowMenuOptions(entity) {
        return [{
            label: 'Ver tableros',
            action: () => this.showUserDashboards(entity)
        }];
    }

    /**
     * Muestra los tableros de un usuario
     * @param {Object} user - El usuario cuyos tableros se mostrarán
     */
    showUserDashboards(user) {
        // Establecer el usuario actual en dashboardManager para que los nuevos tableros
        // se asocien a este usuario específico
        dashboardManager.currentUserId = user.id;
        dashboardManager.currentUserName = user.nombre;

        console.log(`Estableciendo usuario para nuevos tableros: ${user.id} (${user.nombre})`);

        // Crear una nueva instancia de EntityManager para mostrar los tableros
        const dashboardsEntityManager = new EntityManager();

        // Inicializar el gestor con el tipo de entidad 'tableros' y el ID del usuario
        dashboardsEntityManager.init('tableros', {
                companyId: user.empresaId,
                userId: user.id
            })
            .then(entityManagerInstance => {
                // Una vez cargados los datos, mostrar la interfaz
                entityManagerInstance.show();
            })
            .catch(error => {
                console.error(`Error al inicializar el gestor de tableros:`, error);
                showNotification(`Error al cargar tableros: ${error.message}`, 'error');
            });
    }

    /**
     * Maneja el evento de doble clic en una fila de la tabla
     * @param {Object} entity - La entidad (usuario) sobre la que se hizo doble clic
     */
    handleRowDoubleClick(entity) {
        // Al hacer doble clic en un usuario, mostrar sus tableros
        this.showUserDashboards(entity);
    }

    /**
     * Carga los usuarios desde la base de datos
     * @returns {Promise<Array>} - Promesa que se resuelve con la lista de usuarios
     */
    loadEntities(entityManager) {
        return new Promise((resolve, reject) => {
            // Determinar qué usuarios cargar según el tipo de usuario actual
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            if (!currentUser) {
                // Si no hay usuario autenticado, redirigir a login
                authManager.logout();
                // Resolver con array vacío para evitar errores en la UI
                resolve([]);
                return;
            }

            // Obtener el ID de empresa de los parámetros si existe
            let specificCompanyId = null;

            if (entityManager && entityManager.params && entityManager.params.companyId !== undefined) {
                specificCompanyId = Number(entityManager.params.companyId);
            }

            console.log(`loadEntities usuarios: specificCompanyId=${specificCompanyId}, currentCompanyId=${currentCompanyId}`);
            console.log(`loadEntities usuarios: entityManager.params=`, entityManager ? JSON.stringify(entityManager.params) : 'null');

            // Según la especificación, el hash 'usuarios' ha de cargar los usuarios de la empresa del usuario actual
            // Si no hay un ID específico de empresa en los parámetros, usar siempre la empresa del usuario actual
            let usersPromise;

            // Convertir IDs a números para comparaciones consistentes
            const numCurrentCompanyId = Number(currentCompanyId);
            const numSpecificCompanyId = Number(specificCompanyId);
            const isAdmin = currentUser.tipo === 'admin';
            const isCompany1Admin = isAdmin && numCurrentCompanyId === 1;

            if (!specificCompanyId) {
                // Sin ID específico, usar la empresa del usuario actual
                console.log(`loadEntities usuarios: Usando empresa del usuario actual (ID: ${numCurrentCompanyId})`);
                usersPromise = dbManager.getUsers(numCurrentCompanyId);
            } else if (isCompany1Admin) {
                // Administradores de empresa 1 pueden ver usuarios de cualquier empresa
                console.log(`loadEntities usuarios: Admin de empresa 1 viendo usuarios de empresa ${numSpecificCompanyId}`);
                usersPromise = dbManager.getUsers(numSpecificCompanyId);
            } else if (numSpecificCompanyId === numCurrentCompanyId) {
                // Si es la misma empresa del usuario actual, permitir ver los usuarios
                console.log(`loadEntities usuarios: Usuario viendo su propia empresa (ID: ${numSpecificCompanyId})`);
                usersPromise = dbManager.getUsers(numSpecificCompanyId);
            } else {
                // No tiene permisos para ver usuarios de otra empresa
                console.log(`loadEntities usuarios: Usuario sin permisos para ver empresa ${numSpecificCompanyId}`);
                const errorMsg = 'No tiene permisos para ver usuarios de esta empresa';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg)
                    .then(() => {
                        // Hacer logout y volver a la pantalla de login
                        authManager.logout();
                    });
                reject(new Error(errorMsg));
                return;
            }

            usersPromise
                .then(users => {
                    resolve(users);
                })
                .catch(error => {
                    // Mostrar mensaje de error
                    const errorMsg = `Error al obtener usuarios: ${error.message}`;
                    showNotification(errorMsg, 'error');

                    // Mostrar diálogo de error
                    dialogManager.alert(errorMsg)
                        .then(() => {
                            // Navegar a la pantalla principal
                            navigateTo('main-menu');
                        });

                    // Resolver con array vacío para evitar errores en la UI
                    resolve([]);
                });
        });
    }

    /**
     * Guarda un usuario en la base de datos
     * @param {Object} userData - Datos del usuario a guardar
     * @returns {Promise} - Promesa que se resuelve cuando el usuario se ha guardado
     */
    saveEntity(userData) {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores pueden crear/editar usuarios
            if (currentUser.tipo !== 'admin') {
                const errorMsg = 'No tiene permisos para realizar esta acción';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg)
                    .then(() => {
                        // Hacer logout y volver a la pantalla de login
                        authManager.logout();
                    });
                reject(new Error(errorMsg));
                return;
            }

            let specificCompanyId;
            // Obtener el ID de empresa de los parámetros
            if (userData._entity && userData._entity.empresaId) {
                specificCompanyId = userData._entity.empresaId;
            } else
                specificCompanyId = currentCompanyId;

            // Asignar el ID de empresa desde los parámetros (siempre, para nuevos y existentes)
            if (specificCompanyId) {
                userData.empresaId = specificCompanyId;
            } else if (!userData.empresaId) {
                // Si no hay ID de empresa específico en los parámetros, usar el ID de empresa del usuario actual
                userData.empresaId = currentCompanyId;
            }

            // Los administradores que no son de la empresa 1 solo pueden gestionar usuarios de su empresa
            if (currentCompanyId !== 1 && userData.empresaId !== currentCompanyId) {
                const errorMsg = 'Solo puede gestionar usuarios de su empresa';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg)
                    .then(() => {
                        // Hacer logout y volver a la pantalla de login
                        authManager.logout();
                    });
                reject(new Error(errorMsg));
                return;
            }

            // Si es un nuevo usuario, asignar ID y valores por defecto
            if (!userData.id) {
                // Obtener todos los usuarios para determinar el siguiente ID
                dbManager.getAllUsers()
                    .then(users => {
                        const maxId = users.reduce((max, user) => Math.max(max, user.id), 0);
                        userData.id = maxId + 1;

                        // Asignar tipo por defecto si no se especificó
                        if (!userData.tipo) {
                            userData.tipo = 'viewer'; // Visor Tableros por defecto
                        }

                        return dbManager.saveUser(userData);
                    })
                    .then(() => resolve())
                    .catch(error => reject(error));
            } else {
                // Actualizar usuario existente
                dbManager.saveUser(userData)
                    .then(() => resolve())
                    .catch(error => reject(error));
            }
        });
    }

    /**
     * Elimina un usuario de la base de datos
     * @param {number} userId - ID del usuario a eliminar
     * @returns {Promise} - Promesa que se resuelve cuando el usuario se ha eliminado
     */
    deleteEntity(userId) {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores pueden eliminar usuarios
            if (currentUser.tipo !== 'admin') {
                const errorMsg = 'No tiene permisos para realizar esta acción';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg)
                    .then(() => {
                        // Hacer logout y volver a la pantalla de login
                        authManager.logout();
                    });
                reject(new Error(errorMsg));
                return;
            }

            // No permitir eliminar al propio usuario
            if (userId === currentUser.id) {
                const errorMsg = 'No puede eliminar su propio usuario';
                dialogManager.alert(errorMsg);
                reject(new Error(errorMsg));
                return;
            }

            // Obtener el usuario a eliminar para verificar permisos
            dbManager.getAllUsers()
                .then(users => {
                    const userToDelete = users.find(user => user.id === userId);

                    if (!userToDelete) {
                        reject(new Error('Usuario no encontrado'));
                        return;
                    }

                    // Los administradores que no son de la empresa 1 solo pueden eliminar usuarios de su empresa
                    if (currentCompanyId !== 1 && userToDelete.empresaId !== currentCompanyId) {
                        const errorMsg = 'Solo puede eliminar usuarios de su empresa';
                        // Mostrar diálogo de error
                        dialogManager.alert(errorMsg)
                            .then(() => {
                                // Hacer logout y volver a la pantalla de login
                                authManager.logout();
                            });
                        reject(new Error(errorMsg));
                        return;
                    }

                    // Eliminar usuario
                    return dbManager.deleteUser(userId, userToDelete.empresaId);
                })
                .then(success => {
                    if (success) {
                        resolve();
                    } else {
                        reject(new Error('No se pudo eliminar el usuario'));
                    }
                })
                .catch(error => reject(error));
        });
    }
}

// Registrar el manejador de usuarios en el gestor de entidades
app.entityManager.registerEntityHandler('usuarios', new UsuariosEntityHandler());