  /* Animaciones para el cambio de tema */
  @keyframes fadeOut {
      from {
          opacity: 1;
      }

      to {
          opacity: 0.1;
      }
  }

  @keyframes fadeIn {
      from {
          opacity: 0.1;
      }

      to {
          opacity: 1;
      }
  }

  .theme-transition {
      animation-duration: 0.3s;
      animation-fill-mode: forwards;
  }

  .theme-fade-out {
      animation-name: fadeOut;
  }

  .theme-fade-in {
      animation-name: fadeIn;
  }

  /* Estilos para notificaciones */
  #notification-container {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 5000;
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      gap: 10px;
  }

  .notification {
      padding: 12px 20px;
      border-radius: 4px;
      color: white;
      max-width: 300px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
      transform: translateX(100%);
      opacity: 0;
  }

  .notification.show {
      animation: notificationShow 0.3s ease forwards;
  }

  .notification.hide {
      animation: notificationHide 0.3s ease forwards;
  }

  @keyframes notificationShow {
      from {
          transform: translateX(100%);
          opacity: 0;
      }

      to {
          transform: translateX(0);
          opacity: 1;
      }
  }

  @keyframes notificationHide {
      from {
          transform: translateX(0);
          opacity: 1;
      }

      to {
          transform: translateX(100%);
          opacity: 0;
      }
  }

  /* Animaciones para contenedores */
  .container {
      transition: none;
      /* Desactivar transiciones para usar animaciones */
  }

  .container.container-entering {
      animation: containerEnter 0.3s ease forwards;
  }

  .container.container-exiting {
      animation: containerExit 0.3s ease forwards;
  }

  @keyframes containerEnter {
      from {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.95);
      }

      to {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
      }
  }

  @keyframes containerExit {
      from {
          opacity: 1;
          transform: translate(-50%, -50%) scale(1);
      }

      to {
          opacity: 0;
          transform: translate(-50%, -50%) scale(0.95);
      }
  }

  /* Animaciones para modales */
  .modal.modal-entering {
      animation: modalEnter 1.5s ease forwards;
  }

  .modal.modal-exiting {
      animation: modalExit 0.5s ease forwards;
  }

  @keyframes modalEnter {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes modalExit {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  /* Ajuste para contenedores en móvil */
  @media (max-width: 768px) {
      @keyframes containerEnter {
          from {
              opacity: 0;
              transform: scale(0.95);
          }

          to {
              opacity: 1;
              transform: scale(1);
          }
      }

      @keyframes containerExit {
          from {
              opacity: 1;
              transform: scale(1);
          }

          to {
              opacity: 0;
              transform: scale(0.95);
          }
      }
  }

  .notification.info {
      background-color: #2196F3;
  }

  .notification.success {
      background-color: #4CAF50;
  }

  .notification.warning {
      background-color: #FF9800;
  }

  .notification.error {
      background-color: #F44336;
  }

  /* Estilos para diálogos */
  #dialog-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      animation: dialogFadeIn 0.3s ease forwards;
  }

  #dialog-container.dialog-hiding {
      animation: dialogFadeOut 0.3s ease forwards;
  }

  #dialog-container.hidden {
      display: none;
  }

  @keyframes dialogFadeIn {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes dialogFadeOut {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  .dialog {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1000;
      animation: dialogFadeIn 0.3s ease forwards;
  }

  .dialog.dialog-exiting {
      animation: dialogFadeOut 0.3s ease forwards;
      /*   pointer-events: none;*/
      /* Prevent capturing mouse events during exit animation */
  }

  .dialog.hidden {
      display: none;
  }

  .dialog-content {
      background-color: white;
      border-radius: 8px;
      width: 400px;
      max-width: 90%;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
      position: fixed;
      overflow: hidden;
  }

  .dialog-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      border-bottom: 1px solid #eee;
      cursor: move;
  }

  .dialog-header h3 {
      margin: 0;
      font-size: 18px;
  }

  .dialog-header .close-btn {
      font-size: 24px;
      line-height: 1;
      cursor: pointer;
      padding: 0 5px;
      transition: color 0.2s ease;
  }

  .dialog-header .close-btn:hover {
      color: #f44336;
  }

  .dialog-body {
      padding: 20px;
  }

  .dialog-body p {
      margin-top: 0;
      margin-bottom: 20px;
  }

  .dialog-content .button-group {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      padding: 0 20px 20px;
  }

  .dialog-content input[type="text"] {
      width: 100%;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
  }

  .dialog-content input[type="text"]:focus {
      outline: 2px solid var(--primary-color);
      border-color: transparent;
  }

  /* Estilos específicos para cada tipo de diálogo */
  .confirm-dialog .dialog-content {
      border-top: 4px solid var(--primary-color);
  }

  .alert-dialog .dialog-content {
      border-top: 4px solid var(--warning-color);
  }

  .prompt-dialog .dialog-content {
      border-top: 4px solid var(--info-color);
  }

  /* Estilos para el diálogo de confirmación de eliminación */
  .delete-confirm-dialog .dialog-content {
      border-top: 4px solid var(--error-color);
      background-color: rgba(var(--error-color-rgb, 244, 67, 54), 0.05);
  }

  .delete-confirm-dialog .dialog-header {
      background-color: rgba(var(--error-color-rgb, 244, 67, 54), 0.1);
      border-bottom: 1px solid rgba(var(--error-color-rgb, 244, 67, 54), 0.2);
  }

  .delete-confirm-dialog .dialog-header h3 {
      color: var(--error-color);
  }

  .delete-confirm-dailog .dialog-body p {
      text-shadow: 1px 1px #ff0808a3;
  }

  /* Estilos para el menú principal */
  #main-menu-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 20px;
      max-width: 400px;
      height: 500px;
      flex-direction: column;
      margin-top: calc((100vh - 500px)/ 2);
      position: relative !important;
      transform: none !important;
      top: 0 !important;
      left: 0 !important;
  }

  #main-menu-container header {
      width: 100%;
  }

  /* Estilos para el main dentro del contenedor de entidades */
  #entity-management-container main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
  }

  .main-menu-container {
      min-height: unset;
  }

  .main-menu {
      display: flex;
      flex-direction: column;
      gap: 20px;
      align-items: center;
      width: 100%;
      max-width: 600px;
      position: relative;
      /* Necesario para el posicionamiento absoluto del borde */
  }

  /* Estilo para checkboxes en formularios */
  .form-group input[type="checkbox"] {
      margin-right: 0;
      /* Quitamos el margen derecho porque ya lo añadimos en el label */
      cursor: pointer;
      width: auto;
      /* Evitar que tome el ancho del 100% */
  }

  /* Estilo para el contenedor de checkbox */
  .form-group .checkbox-container {
      display: flex;
      align-items: center;
      margin-bottom: 5px;
  }

  /* Estilo para las etiquetas de checkbox */
  .form-group .checkbox-container label {
      margin-bottom: 0;
      cursor: pointer;
  }

  /* Estilo para los campos de tipo textarea en formularios de entidades */
  .form-group textarea {
      width: 100%;
      min-height: 100px;
      padding: 8px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      resize: vertical;
  }

  .menu-button {
      display: flex;
      align-items: center;
      width: 100%;
      padding: 15px 20px;
      font-size: 18px;
      border-radius: 8px;
      background-color: var(--surface-color);
      color: var(--text-color);
      border: 1px solid var(--border-color);
      cursor: pointer;
      /* Estilos adicionales para los divs */
      box-sizing: border-box;
      user-select: none;
      outline: none;
  }

  .menu-button .icon {
      margin-right: 15px;
      font-size: 24px;
  }

  /* Div con borde para el menú principal */
  .menu-border {
      position: absolute;
      width: 100%;
      height: 100%;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      pointer-events: none;
      /* Para que no interfiera con los clics */
      box-sizing: border-box;
      transition: top 0.3s ease;
      /* Transición suave para el movimiento vertical */
      z-index: 10;
      /* Alto para que quede por encima de las opciones */
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.05);
      /* Fondo semi-transparente */
  }

  .logout-btn {
      background-color: transparent;
      color: var(--text-color);
      border: none;
      padding: 8px;
      cursor: pointer;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.3s ease;
  }

  .logout-btn:hover {
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.1);
  }

  .logout-icon {
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: transform 0.3s ease;
  }

  .logout-btn:hover .logout-icon {
      transform: rotate(90deg);
  }

  .back-btn {
      background-color: transparent;
      color: var(--text-color);
      border: none;
      padding: 8px 15px;
      cursor: pointer;
      font-size: 14px;
  }

  /* Estilos para la gestión de entidades */
  .entity-container {
      padding: 10px;
      padding-top: 0px;
      /* Eliminar el espacio superior */
      max-width: 100%;
      width: calc(100vw - 6em);
      /* Ancho para ordenador */
      display: flex;
      flex-direction: column;
      flex: 1;
      /* Tomar todo el espacio disponible */
      overflow: hidden;
      /* Evitar scroll en el contenedor principal */
  }

  .entity-count {
      font-size: 14px;
      color: var(--text-secondary-color);
      margin-left: 10px;
  }

  .entity-filter-container {
      display: flex;
      margin-bottom: 10px;
      position: relative;
      flex-shrink: 0;
      /* Evita que el filtro se encoja */
      z-index: 5;
      /* Subir el z-index para que esté por encima de otros elementos */
  }

  .entity-filter {
      flex: 1;
      padding: 10px;
      padding-right: 30px;
      /* Espacio para el botón de limpiar */
      border: none;
      border-radius: 4px;
      font-size: 16px;
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.05);
      transition: border-color 0.2s ease;
  }

  .entity-filter:focus {
      border: 1px solid var(--border-color);
      outline: none;
  }

  .clear-filter-btn {
      position: absolute;
      right: 10px;
      top: 20px;
      background: none;
      border: none;
      font-size: 20px;
      color: var(--text-secondary-color);
      cursor: pointer;
      padding: 0;
      width: 20px;
      height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 1;
      /* Asegurar que esté por encima del input */
  }

  .entity-table-container {
      overflow-y: auto;
      /* Permitir scroll vertical */
      overflow-x: auto;
      /* Mantener scroll horizontal */
      width: 100%;
      flex-grow: 1;
      /* Tomar el espacio restante */
      min-height: 300px;
      /* Altura mínima para asegurar que se vea el scroll */
      max-height: calc(100vh - 225px);
      /* Altura máxima para asegurar que no se salga de la pantalla */
      scrollbar-width: thin;
      /* Barra de desplazamiento delgada en Firefox */
      scrollbar-color: var(--primary-color) transparent;
      /* Color de la barra de desplazamiento en Firefox */
      border: 1px solid var(--border-color);
      /* Borde para visualizar mejor el contenedor */
      padding-right: 5px;
      /* Espacio para la barra de scroll */
  }

  /* Estilos para barras de desplazamiento en WebKit */
  .entity-table-container::-webkit-scrollbar {
      width: 16px;
      height: 16px;
  }

  .entity-table-container::-webkit-scrollbar-track {
      background: rgba(0, 0, 0, 0.05);
      border-radius: 6px;
  }

  .entity-table-container::-webkit-scrollbar-thumb {
      background-color: var(--primary-color);
      border-radius: 6px;
      border: 2px solid transparent;
      background-clip: padding-box;
  }

  .entity-table-container::-webkit-scrollbar-thumb:hover {
      background-color: var(--secondary-color);
  }

  /* Estilos específicos para temas */
  .theme-tron .entity-table-container {
      scrollbar-color: var(--accent-color) rgba(0, 0, 0, 0.2);
  }

  .theme-tron .entity-table-container::-webkit-scrollbar-track {
      background: rgba(0, 162, 255, 0.1);
  }

  .theme-tron .entity-table-container::-webkit-scrollbar-thumb {
      background-color: var(--accent-color);
      box-shadow: 0 0 5px var(--glow-effect);
  }

  .theme-neumorphic .entity-table-container {
      scrollbar-color: var(--primary-color) var(--surface-color);
  }

  .theme-neumorphic .entity-table-container::-webkit-scrollbar-track {
      background: var(--surface-color);
      box-shadow: var(--shadow-inset);
  }

  .theme-neumorphic .entity-table-container::-webkit-scrollbar-thumb {
      background-color: var(--primary-color);
      box-shadow: var(--shadow-small);
  }

  .entity-table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      border: 2px solid var(--border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      table-layout: fixed;
      /* Fijar el ancho de las columnas */
  }

  /* Ancho mínimo para las columnas (excepto las dos primeras) */
  .entity-table th:not(:nth-child(1)):not(:nth-child(2)),
  .entity-table td:not(:nth-child(1)):not(:nth-child(2)) {
      min-width: 70px;
      width: max-content;
  }

  .entity-table th {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      border-right: 1px solid var(--border-color);
      overflow-wrap: normal;
      /* Solo romper por espacios en blanco */
      word-break: normal;
      /* Solo romper por espacios en blanco */
      white-space: normal;
      /* Permitir múltiples líneas */
  }

  .entity-table td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid var(--border-color);
      border-right: 1px solid var(--border-color);
      overflow-wrap: break-word;
      /* Permitir que el texto se rompa para evitar desbordamiento */
      word-break: break-word;
      /* Compatibilidad adicional */
  }

  .entity-table th:last-child,
  .entity-table td:last-child {
      border-right: none;
  }

  .entity-table th {
      background-color: var(--primary-color);
      color: white;
      font-weight: bold;
      position: relative;
      cursor: pointer;
      border-bottom: 2px solid var(--border-color);
  }

  .entity-table th:hover {
      background-color: var(--secondary-color);
  }

  .entity-table th.sortable::after {
      content: '';
      display: inline-block;
      width: 0;
      height: 0;
      margin-left: 5px;
  }

  .entity-table th.sort-asc::after {
      content: '▲';
      font-size: 10px;
  }

  .entity-table th.sort-desc::after {
      content: '▼';
      font-size: 10px;
  }

  .entity-table tr:nth-child(odd) {
      background-color: var(--surface-color);
  }

  .entity-table tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.03);
  }

  .entity-table tr:hover {
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.1);
  }

  .entity-table .checkbox-cell {
      width: 40px;
      text-align: center;
      vertical-align: middle;
      padding-left: 0;
      padding-right: 0;
  }

  .entity-table .action-cell {
      width: 40px;
      text-align: center;
      vertical-align: middle;
      padding-left: 0;
      padding-right: 0;
  }

  .entity-table .action-button {
      background: none;
      border: none;
      cursor: pointer;
      font-size: 18px;
      line-height: 1;
      padding: 0;
      margin: 0;
      height: 24px;
      width: 24px;
      border-radius: 3px;
      transition: all 0.2s ease;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      vertical-align: middle;
  }

  .entity-table .action-button:hover {
      background-color: rgba(0, 0, 0, 0.05);
  }

  .entity-action-menu {
      position: absolute;
      background-color: var(--surface-color);
      border: 1px solid var(--border-color);
      border-radius: 4px;
      box-shadow: 0 2px 10px var(--shadow-color);
      z-index: 1205;
      min-width: 150px;
  }

  .entity-action-menu button {
      display: block;
      width: 100%;
      text-align: left;
      padding: 10px 15px;
      background: none;
      border: none;
      border-bottom: 1px solid var(--border-color);
      color: var(--text-color);
      cursor: pointer;
  }

  .entity-action-menu button:last-child {
      border-bottom: none;
  }

  .entity-action-menu button:hover {
      background-color: var(--primary-color);
      color: white;
  }

  .entity-action-menu .danger-option {
      color: var(--error-color);
  }

  .entity-action-menu button.disabled {
      opacity: 0.5;
      cursor: not-allowed;
  }

  .entity-action-menu button.disabled:hover {
      background-color: var(--surface-color);
      color: var(--text-color);
  }

  .entity-action-menu .danger-option.disabled:hover {
      color: var(--error-color);
  }

  /* Estilos para navegación de entidades */
  .entity-nav-container {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      padding: 5px 0;
  }

  .nav-button {
      background-color: var(--primary-color);
      color: white;
      border: none;
      border-radius: 4px;
      padding: 8px 15px;
      font-size: 18px;
      cursor: pointer;
      transition: all 0.2s ease;
  }

  .nav-button:hover {
      background-color: var(--secondary-color);
      transform: scale(1.05);
  }

  .nav-button:disabled {
      background-color: #ccc;
      cursor: not-allowed;
      opacity: 0.6;
      transform: none;
  }

  /* Estilos para las flechas de navegación */
  .form-with-nav-container {
      position: relative;
      width: 100%;
  }

  /* Botones de navegación en escritorio */
  .side-nav-button {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      z-index: 10;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .side-nav-button.prev-button {
      left: -20px;
  }

  .side-nav-button.next-button {
      right: -20px;
  }

  /* Botones de navegación comunes */
  .nav-button {
      font-size: 16px;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px;
  }

  /* Botones de icono */
  .icon-btn {
      min-width: 40px !important;
      width: auto !important;
      padding: 0 10px !important;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 38px !important;
      /* Altura fija para todos los botones */
  }

  /* Contenedor para botones de navegación en escritorio */
  .desktop-nav-container {
      display: flex;
      gap: 5px;
  }

  /* Botones de navegación en escritorio */
  .desktop-nav-button {
      min-width: 40px !important;
      width: auto !important;
      padding: 0 10px !important;
  }

  /* Botones de navegación en móvil */
  @media (max-width: 768px) {

      /* Ocultar botones laterales en móvil */
      .side-nav-button {
          display: none;
      }

      /* Mostrar botones de navegación en la barra de botones */
      .mobile-nav-button {
          font-size: 16px;
          padding: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          height: 38px;
      }

      /* Ajustar tamaño de botones en móvil */
      .button-group button {
          min-width: unset !important;
          padding: 8px 12px;
          font-size: 14px;
          height: 38px;
      }

      /* Ajustar el grupo de botones para que use flexbox */
      .button-group {
          display: flex !important;
          flex-direction: row !important;
          gap: 3px !important;
          width: 100%;
      }

      /* Filas de botones */
      .button-row {
          display: flex;
          width: 100%;
          gap: 5px;
      }

      /* Contenedor para botones de navegación */
      .nav-buttons-container {
          display: flex;
          gap: 5px;
          flex: 1;
      }

      /* Ajustar el botón primario */
      .button-group .primary-btn {
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de opciones */
      #entity-options-btn {
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de cancelar */
      #entity-cancel-btn {
          height: 38px;
          display: flex;
          align-items: center;
          justify-content: center;
      }
  }

  /* Estilos para escritorio */
  @media (min-width: 769px) {

      /* Ajustar el grupo de botones para escritorio */
      .button-group {
          display: flex !important;
          flex-direction: row !important;
          gap: 10px !important;
          width: 100%;
          justify-content: flex-end;
          align-items: center;
          /* Alinear verticalmente todos los botones */
      }

      /* Ajustar el botón primario para escritorio */
      .button-group .primary-btn {
          margin-right: auto;
          /* Empuja los otros botones hacia la derecha */
          height: 38px !important;
          /* Altura fija para todos los botones */
      }

      /* Ajustar todos los botones en escritorio para que tengan la misma altura */
      .button-group button {
          height: 38px !important;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de opciones en escritorio */
      #entity-options-btn {
          height: 38px !important;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el botón de cancelar en escritorio */
      #entity-cancel-btn {
          height: 38px !important;
          display: flex;
          align-items: center;
          justify-content: center;
      }

      /* Ajustar el contenedor de navegación en escritorio */
      .desktop-nav-container {
          height: 38px !important;
          display: flex;
          align-items: center;
      }

      /* Ajustar el ancho del modal para escritorio */
      .modal-content {
          width: 600px;
          max-width: calc(100vw - 4em);
      }
  }

  /* Icono de hamburguesa para el botón de opciones */
  .hamburger-icon {
      font-size: 20px;
      transition: transform 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
  }

  .hamburger-icon.menu-opening {
      transform: rotate(90deg);
  }

  .hamburger-icon.menu-closing {
      transform: rotate(0deg);
  }

  /* Ajustes para el menú de opciones en móvil */
  @media (max-width: 768px) {
      .entity-options-menu {
          max-height: 80vh;
          overflow-y: auto;
          position: fixed;
          /* Cambiado de absolute a fixed para asegurar que esté por encima de todo */
          width: fit-content;
          max-width: 300px;
          box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
          /* Asegurar que esté por encima del formulario */
          /* Se eliminó bottom: 100% para permitir posicionamiento dinámico */
          left: 0 !important;
          margin-bottom: 10px;
          /* Espacio entre el menú y el botón */
      }
  }

  /* Estilos para la configuración de columnas */
  .columns-container {
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      margin-bottom: 20px;
  }

  /* Animaciones para modales */
  .modal {
      transition: none;
      /* Desactivar transiciones para usar animaciones */
  }

  .modal.modal-entering {
      animation: modalFadeIn 0.5s ease forwards;
  }

  .modal.modal-exiting {
      animation: modalFadeOut 0.5s ease forwards;
  }

  @keyframes modalFadeIn {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes modalFadeOut {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  /* Estilos específicos para el modal de configuración de columnas */
  #column-config-modal .modal-content {
      width: 430px !important;
      /* Ancho fijo */
      overflow: hidden;
      /* Solo permitir scroll en el contenedor de columnas */
      display: flex;
      flex-direction: column;
  }

  #column-config-modal .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-top: 5px;
      /* Reducir espacio superior */
      margin-bottom: 10px;
  }

  #column-config-modal .modal-header h2 {
      margin: 0;
  }

  #column-config-modal .close-btn {
      float: none;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1;
  }

  .column-item {
      display: flex;
      align-items: center;
      padding: 10px;
      border-bottom: 1px solid var(--border-color);
      cursor: grab;
      flex-wrap: wrap;
      /* Permitir que los elementos se envuelvan */
      row-gap: 5px;
  }

  .column-item:last-child {
      border-bottom: none;
  }

  .column-item.drag-over {
      background-color: rgba(var(--primary-color-rgb, 76, 175, 80), 0.1);
  }

  .column-item .drag-handle {
      margin-right: 10px;
      color: var(--text-secondary-color);
      cursor: grab;
  }

  .column-item input[type="checkbox"] {
      margin-right: 10px;
  }

  .column-item label {
      flex: 1;
      cursor: pointer;
      word-break: break-word;
      /* Permitir que el texto se rompa */
      white-space: normal;
      /* Permitir múltiples líneas */
  }

  /* Estilos para widget de gráfica */
  .chart-widget .widget-content {
      height: 100%;
      width: 100%;
      padding: 5px;
      box-sizing: border-box;
  }

  .chart-container {
      height: 100%;
      width: 100%;
      position: relative;
  }

  /* Asegurar que el contenedor de la gráfica ocupe todo el espacio disponible */
  .chart-widget .widget-content {
      display: flex;
      justify-content: center;
      align-items: center;
  }

  /* Ajustar el botón de login */
  #login-btn {
      width: 100%;
  }



  .modal {
      /* Estilos base para móvil vertical y ordenador */
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      max-width: 90%;
      max-height: 95vh;
      /* Aumentado de 90vh a 95vh */
      overflow: auto;
      /* Añade estos estilos adicionales según necesidades de diseño */
      /* width: auto;
      background: white;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
      */
      padding: 20px;
      border-radius: 8px;
  }

  @media (max-width: 767px) and (orientation: landscape) {
      .modal {
          /* Estilos específicos para móvil horizontal */
          height: 100vh;
          max-height: none;
          top: 0;
          transform: translateX(-50%);
          border-radius: 0;
      }
  }


  /* Animaciones para mostrar y ocultar elementos.*/
  .show-animation {
      animation: show 0.3s forwards;
  }

  .hide-animation {
      animation: hide 0.3s forwards;
  }

  @keyframes show {
      from {
          opacity: 0;
      }

      to {
          opacity: 1;
      }
  }

  @keyframes hide {
      from {
          opacity: 1;
      }

      to {
          opacity: 0;
      }
  }

  /* Estilo para todos los inputs numéricos */
  .form-group:has(input[type="number"]) {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
  }

  .form-group:has(input[type="number"]) label {
      flex: 0.5;
      /* Toma el 50% del espacio disponible */
      margin-bottom: 0;
      /* Anula el margen inferior existente si lo hubiera */
  }

  .form-group:has(input[type="number"]) input[type="number"] {
      flex: 0.5;
      /* Toma el 50% del espacio disponible */
      min-width: 80px;
      /* Ancho mínimo para usabilidad */
  }

  /* Alternativa para navegadores que no soportan :has */
  .form-group input[type="number"] {
      width: 50%;
      display: inline-block;
      vertical-align: middle;
  }

  .form-group label {
      width: 50%;
      display: inline-block;
      vertical-align: middle;
  }

  .widget.selected::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: var(--selection-overlay);
      border: 2px solid var(--selection-border);
      pointer-events: none;
      z-index: 1;
  }

  .widget.selected::after {
      content: '✓';
      position: absolute;
      top: 5px;
      right: 5px;
      width: 20px;
      height: 20px;
      background-color: var(--selection-check-background);
      color: var(--selection-check);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: bold;
      z-index: 2;
  }

  .widget .widget-checkbox-container {
      position: absolute;
      top: 5px;
      right: 5px;
      z-index: 2;
      display: none;
      /* Oculto por defecto */
  }

  /* Mostrar checkboxes cuando el body tiene la clase selection-mode */
  body.selection-mode .widget .widget-checkbox-container {
      display: block;
  }

  /* Efecto de flash al pegar widgets - Estilos base */
  .widget-pasted {
      animation: widget-paste-flash 1s ease-out;
  }

  @keyframes widget-paste-flash {
      0% {
          transform: scale(0.95);
          opacity: 0.7;
      }

      50% {
          transform: scale(1.02);
          opacity: 1;
      }

      100% {
          transform: scale(1);
      }
  }

  /* Estilos genéricos para botones de series */
  .add-series-btn,
  .remove-series-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 24px;
      height: 24px;
      padding: 0;
      margin: 0 5px;
      border-radius: 0;
      position: relative;
      z-index: 5;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      border: none;
      color: white;
      text-align: center;
      font-size: 18px;
      font-weight: bold;
      line-height: 1;
      font-family: Arial, sans-serif;
  }

  .add-series-btn:hover,
  .remove-series-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  }

  .add-series-btn {
      background-color: var(--success-color, #4CAF50);
  }

  .remove-series-btn {
      background-color: var(--error-color, #F44336);
  }