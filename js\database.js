/**
 * Módulo para la gestión de la base de datos en localStorage
 * Implementado con promesas para simular una API REST
 */
class DatabaseManager {
    constructor() {
        this.dbName = 'ipram_db';
        this.initialized = false;
        this.db = {};
    }

    /**
     * Inicializa la base de datos
     * @returns {Promise} - Promesa que se resuelve cuando la base de datos está inicializada
     */
    init() {
        return new Promise((resolve, reject) => {
            try {
                if (this.initialized) {
                    resolve();
                    return;
                }

                // Cargar la base de datos
                this.loadDatabase()
                    .then(db => {
                        if (!db) {
                            // Si no hay base de datos, crear una vacía
                            return this.createEmptyDatabase();
                        }
                        return db;
                    })
                    .then(db => {
                        this.db = db;
                        this.initialized = true;
                        resolve();
                    })
                    .catch(error => {
                        console.error('Error al inicializar la base de datos:', error);
                        reject(error);
                    });
            } catch (error) {
                console.error('Error al inicializar la base de datos:', error);
                reject(error);
            }
        });
    }

    /**
     * Carga la base de datos desde localStorage
     * @returns {Promise} - Promesa que se resuelve con el objeto de base de datos
     */
    loadDatabase() {
        return new Promise((resolve, reject) => {
            try {
                const dbJson = localStorage.getItem(this.dbName);
                if (dbJson) {
                    const db = JSON.parse(dbJson);

                    // Asegurarse de que todos los widgets tengan coordenadas válidas
                    this.ensureValidWidgetPositions(db);

                    resolve(db);
                } else {
                    resolve(null);
                }
            } catch (error) {
                console.error('Error al cargar la base de datos:', error);
                reject(error);
            }
        });
    }

    /**
     * Asegura que todos los widgets en la base de datos tengan coordenadas válidas
     * @param {Object} db - Base de datos
     */
    ensureValidWidgetPositions(db) {
        // Recorrer todas las empresas
        for (const key in db) {
            if (key.startsWith('empresa_') && db[key] && db[key].tableros) {
                // Recorrer todos los tableros de la empresa
                db[key].tableros.forEach(tablero => {
                    if (tablero.widgets && Array.isArray(tablero.widgets)) {
                        // Recorrer todos los widgets del tablero
                        tablero.widgets.forEach(widget => {
                            // Asegurarse de que x e y sean números válidos
                            if (widget.x === undefined || widget.x === null || isNaN(widget.x)) {
                                console.warn(`Widget ${widget.id} tiene coordenada x inválida, estableciendo a 0`);
                                widget.x = 0;
                            }
                            if (widget.y === undefined || widget.y === null || isNaN(widget.y)) {
                                console.warn(`Widget ${widget.id} tiene coordenada y inválida, estableciendo a 0`);
                                widget.y = 0;
                            }

                            // Convertir a número si son strings
                            widget.x = Number(widget.x);
                            widget.y = Number(widget.y);
                        });
                    }
                });
            }
        }
    }

    /**
     * Crea una estructura de base de datos vacía
     * @returns {Promise} - Promesa que se resuelve con la estructura de base de datos vacía
     */
    createEmptyDatabase() {
        return new Promise((resolve) => {
            const emptyDb = {};
            resolve(emptyDb);
        });
    }

    /**
     * Guarda la base de datos en localStorage
     * @returns {Promise} - Promesa que se resuelve cuando la base de datos se ha guardado
     */
    /*saveDatabase() {
        return new Promise((resolve, reject) => {
            try {
                // Crear una copia limpia de la base de datos para evitar referencias circulares
                const cleanDb = this.createCleanCopy(this.db);

                // Convertir a JSON sin necesidad de un replacer especial
                const jsonString = JSON.stringify(cleanDb);
                localStorage.setItem(this.dbName, jsonString);
                resolve();
            } catch (error) {
                console.error('Error al guardar la base de datos:', error);
                reject(error);
            }
        });
    }*/
    saveDatabase() {
        return new Promise((resolve, reject) => {
            try {
                // Crear una copia limpia de la base de datos para evitar referencias circulares
                const cleanDb = {};

                // Procesar cada clave de la base de datos
                for (const key in this.db) {
                    if (Object.prototype.hasOwnProperty.call(this.db, key)) {
                        // Si es una empresa, procesar sus tableros
                        if (key.startsWith('empresa_')) {
                            const companyData = this.db[key];
                            const cleanCompanyData = {
                                ...companyData
                            };

                            // Si la empresa tiene tableros, limpiar cada uno
                            if (cleanCompanyData.tableros && Array.isArray(cleanCompanyData.tableros)) {
                                cleanCompanyData.tableros = cleanCompanyData.tableros.map(dashboard => {
                                    const cleanDashboard = {
                                        ...dashboard
                                    };

                                    // Limpiar los widgets del tablero
                                    if (cleanDashboard.widgets && Array.isArray(cleanDashboard.widgets)) {
                                        try {
                                            cleanDashboard.widgets = this.createCleanCopy(cleanDashboard.widgets);
                                        } catch (error) {
                                            console.error('Error al limpiar widgets del tablero:', error);
                                            // Intentar limpiar cada widget individualmente
                                            cleanDashboard.widgets = cleanDashboard.widgets.map(widget => {
                                                try {
                                                    return this.createCleanCopy(widget);
                                                } catch (widgetError) {
                                                    console.error(`Error al limpiar widget ${widget.id} de tipo ${widget.type}:`, widgetError);
                                                    // Crear una versión simplificada del widget sin propiedades problemáticas
                                                    const safeWidget = {
                                                        ...widget
                                                    };
                                                    // Eliminar propiedades que pueden causar problemas
                                                    ['chart', 'periodChart', '_chart', 'chartInstance', 'canvas', 'ctx'].forEach(prop => {
                                                        if (safeWidget[prop]) delete safeWidget[prop];
                                                    });
                                                    return safeWidget;
                                                }
                                            });
                                        }
                                    }

                                    return cleanDashboard;
                                });
                            }

                            cleanDb[key] = cleanCompanyData;
                        } else {
                            // Para otras claves, copiar directamente
                            cleanDb[key] = this.db[key];
                        }
                    }
                }

                // Convertir a JSON y guardar
                const jsonString = app.stringify(cleanDb);
                localStorage.setItem(this.dbName, jsonString);
                resolve();
            } catch (error) {
                console.error('Error al guardar la base de datos:', error);
                reject(error);
            }
        });
    }

    /**
     * Crea una copia limpia de un objeto, eliminando solo propiedades problemáticas
     * y preservando todas las demás propiedades importantes
     * @param {Object} obj - Objeto a limpiar
     * @returns {Object} - Copia limpia del objeto
     */
    createCleanCopy(obj) {
        // Para objetos simples, devolver una copia directa
        if (typeof obj !== 'object' || obj === null) {
            return obj;
        }

        // Para arrays, mapear cada elemento recursivamente
        if (Array.isArray(obj)) {
            return obj.map(item => this.createCleanCopy(item));
        }

        // Para objetos, crear una copia directa usando JSON
        // Esto es más seguro que copiar propiedad por propiedad
        let cleanObj;

        try {
            // Intentar una copia directa usando app.stringify
            cleanObj = JSON.parse(app.stringify(obj));
            return cleanObj;
        } catch (error) {
            // Si hay error (probablemente por referencias circulares), usar el método manual
            console.log("Error al copiar objeto con app.stringify, usando método manual:", error.message);
            cleanObj = {};

            // Lista de propiedades a excluir (que pueden causar problemas)
            const excludeProps = ['chart', '_chart', 'chartInstance', 'canvas', 'ctx', 'periodChart'];

            for (const key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    // Saltar propiedades a excluir
                    if (excludeProps.includes(key)) continue;

                    const value = obj[key];

                    // Saltar funciones
                    if (typeof value === 'function') continue;

                    // Procesar según el tipo
                    if (typeof value === 'object' && value !== null) {
                        cleanObj[key] = this.createCleanCopy(value);
                    } else {
                        cleanObj[key] = value;
                    }
                }
            }

            return cleanObj;
        }
    }

    /**
     * Obtiene los datos de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con los datos de la empresa
     */
    getCompanyData(companyId) {
        return new Promise((resolve) => {
            const companyKey = `empresa_${companyId}`;
            resolve(this.db[companyKey] || null);
        });
    }

    /**
     * Guarda los datos de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {Object} data - Datos a guardar
     * @returns {Promise} - Promesa que se resuelve cuando los datos se han guardado
     */
    saveCompanyData(companyId, data) {
        return new Promise((resolve, reject) => {
            try {
                const companyKey = `empresa_${companyId}`;

                // Crear una copia de los datos para no modificar el original
                const cleanData = {
                    ...data
                };

                // Si hay tableros, asegurarse de que los widgets se guarden correctamente
                if (cleanData.tableros && Array.isArray(cleanData.tableros)) {
                    // Procesar todos los tableros para asegurar que los widgets se guarden correctamente
                    cleanData.tableros.forEach(dashboard => {
                        if (dashboard.widgets && Array.isArray(dashboard.widgets)) {
                            // Hacer una copia limpia de los widgets, preservando todas las propiedades importantes
                            const cleanWidgets = [];

                            dashboard.widgets.forEach(widget => {
                                // Crear una copia limpia del widget
                                const cleanWidget = this.createCleanCopy(widget);

                                // Asegurarse de que el estilo esté correctamente definido
                                if (!cleanWidget.style) {
                                    cleanWidget.style = {
                                        backgroundColor: "defecto",
                                        textColor: "defecto",
                                        borderColor: "defecto"
                                    };
                                } else {
                                    // Asegurarse de que todas las propiedades de estilo estén definidas
                                    cleanWidget.style.backgroundColor = cleanWidget.style.backgroundColor || "defecto";
                                    cleanWidget.style.textColor = cleanWidget.style.textColor || "defecto";
                                    cleanWidget.style.borderColor = cleanWidget.style.borderColor || "defecto";
                                }

                                cleanWidgets.push(cleanWidget);
                            });

                            // Reemplazar los widgets originales con los limpios
                            dashboard.widgets = cleanWidgets;
                        }
                    });

                    console.log(`Procesados widgets de ${cleanData.tableros.length} tableros para guardar`);
                }

                this.db[companyKey] = cleanData;
                this.saveDatabase()
                    .then(() => {
                        // Restaurar los datos originales
                        if (data.tableroActual && data.tableros && Array.isArray(data.tableros)) {
                            const currentDashboardIndex = data.tableros.findIndex(d => d.id === data.tableroActual);

                            if (currentDashboardIndex >= 0) {
                                // No es necesario hacer nada, los datos originales no se modificaron
                            }
                        }
                        resolve();
                    })
                    .catch(error => reject(error));
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Obtiene todas las empresas
     * @returns {Promise} - Promesa que se resuelve con la lista de empresas
     */
    getAllCompanies() {
        return new Promise((resolve) => {
            const companies = [];
            for (const key in this.db) {
                if (key.startsWith('empresa_')) {
                    const companyData = this.db[key];
                    if (companyData.info) {
                        companies.push(companyData.info);
                    }
                }
            }
            resolve(companies);
        });
    }

    /**
     * Obtiene una empresa por su ID
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con la empresa
     */
    getCompany(companyId) {
        return this.getCompanyData(companyId)
            .then(companyData => companyData ? companyData.info : null);
    }

    /**
     * Guarda una empresa
     * @param {Object} company - Empresa a guardar
     * @returns {Promise} - Promesa que se resuelve cuando la empresa se ha guardado
     */
    saveCompany(company) {
        return new Promise((resolve, reject) => {
            const companyId = company.id;

            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData) {
                        companyData = {
                            info: company,
                            usuarios: [],
                            tableros: [],
                            tableroActual: null,
                            temaActual: 'tron'
                        };
                    } else {
                        companyData.info = company;
                    }

                    return this.saveCompanyData(companyId, companyData);
                })
                .then(() => resolve())
                .catch(error => reject(error));
        });
    }

    /**
     * Elimina una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteCompany(companyId) {
        return new Promise((resolve, reject) => {
            try {
                const companyKey = `empresa_${companyId}`;
                if (this.db[companyKey]) {
                    delete this.db[companyKey];
                    this.saveDatabase()
                        .then(() => resolve(true))
                        .catch(error => reject(error));
                } else {
                    resolve(false);
                }
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Obtiene los usuarios de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con la lista de usuarios
     */
    getUsers(companyId) {
        return this.getCompanyData(companyId)
            .then(companyData => companyData ? companyData.usuarios || [] : []);
    }

    /**
     * Obtiene todos los usuarios de todas las empresas
     * @returns {Promise} - Promesa que se resuelve con la lista de todos los usuarios
     */
    getAllUsers() {
        return new Promise((resolve) => {
            const allUsers = [];
            for (const key in this.db) {
                if (key.startsWith('empresa_')) {
                    const companyData = this.db[key];
                    if (companyData.usuarios && Array.isArray(companyData.usuarios)) {
                        allUsers.push(...companyData.usuarios);
                    }
                }
            }
            resolve(allUsers);
        });
    }

    /**
     * Obtiene un usuario por su login
     * @param {string} login - Login del usuario
     * @returns {Promise} - Promesa que se resuelve con el usuario
     */
    getUserByLogin(login) {
        return this.getAllUsers()
            .then(allUsers => allUsers.find(user => user.login === login) || null);
    }

    /**
     * Guarda un usuario
     * @param {Object} user - Usuario a guardar
     * @returns {Promise} - Promesa que se resuelve cuando el usuario se ha guardado
     */
    saveUser(user) {
        return new Promise((resolve, reject) => {
            const companyId = user.empresaId;

            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData) {
                        reject(new Error(`La empresa con ID ${companyId} no existe`));
                        return;
                    }

                    if (!companyData.usuarios) {
                        companyData.usuarios = [];
                    }

                    // Buscar si el usuario ya existe
                    const index = companyData.usuarios.findIndex(u => u.id === user.id);

                    if (index >= 0) {
                        // Actualizar usuario existente
                        companyData.usuarios[index] = user;
                    } else {
                        // Añadir nuevo usuario
                        companyData.usuarios.push(user);
                    }

                    return this.saveCompanyData(companyId, companyData);
                })
                .then(() => resolve())
                .catch(error => reject(error));
        });
    }

    /**
     * Elimina un usuario
     * @param {number} userId - ID del usuario
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteUser(userId, companyId) {
        return new Promise((resolve, reject) => {
            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData || !companyData.usuarios) {
                        resolve(false);
                        return;
                    }

                    const initialLength = companyData.usuarios.length;
                    companyData.usuarios = companyData.usuarios.filter(user => user.id !== userId);

                    if (companyData.usuarios.length !== initialLength) {
                        return this.saveCompanyData(companyId, companyData)
                            .then(() => resolve(true));
                    } else {
                        resolve(false);
                    }
                })
                .catch(error => reject(error));
        });
    }

    /**
     * Obtiene los tableros de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {number} [userId] - ID del usuario para filtrar (opcional)
     * @returns {Promise} - Promesa que se resuelve con la lista de tableros, cada uno con el ID de empresa asignado
     */
    getDashboards(companyId, userId) {
        return this.getCompanyData(companyId)
            .then(companyData => {
                if (!companyData) {
                    return [];
                }

                // Obtener los tableros o un array vacío si no hay
                let dashboards = companyData.tableros || [];

                // Si se especificó un userId, filtrar los tableros por ese usuario
                if (userId) {
                    dashboards = dashboards.filter(dashboard =>
                        dashboard.userId === userId || !dashboard.userId);
                }

                // Asegurarse de que cada tablero tenga el ID de empresa
                return Promise.all(dashboards.map(async dashboard => {
                    // Si el tablero no tiene userId, asignar el primer usuario de la empresa
                    if (!dashboard.userId && companyData.usuarios && companyData.usuarios.length > 0) {
                        dashboard.userId = companyData.usuarios[0].id;
                    }

                    // Buscar el nombre del usuario en la empresa del tablero
                    let userName = '';
                    if (dashboard.userId) {
                        // Primero buscar en la empresa del tablero
                        const user = companyData.usuarios?.find(u => u.id === dashboard.userId);
                        if (user) {
                            userName = user.nombre;
                        } else {
                            // Si no está en la empresa del tablero, buscar en la empresa 1 (admin)
                            if (companyId !== 1) {
                                const company1Data = await this.getCompanyData(1);
                                const adminUser = company1Data?.usuarios?.find(u => u.id === dashboard.userId);
                                if (adminUser) {
                                    userName = adminUser.nombre;
                                }
                            }
                        }
                    }

                    return {
                        ...dashboard,
                        empresaId: companyId,
                        userName: userName || 'Usuario desconocido'
                    };
                }));
            });
    }

    /**
     * Obtiene el ID del tablero actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con el ID del tablero actual
     */
    getCurrentDashboardId(companyId) {
        return this.getCompanyData(companyId)
            .then(companyData => companyData ? companyData.tableroActual : null);
    }

    /**
     * Establece el tablero actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {number} dashboardId - ID del tablero
     * @returns {Promise} - Promesa que se resuelve cuando se ha establecido el tablero actual
     */
    setCurrentDashboardId(companyId, dashboardId) {
        return new Promise((resolve, reject) => {
            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData) {
                        reject(new Error(`La empresa con ID ${companyId} no existe`));
                        return;
                    }

                    companyData.tableroActual = dashboardId;
                    return this.saveCompanyData(companyId, companyData);
                })
                .then(() => resolve())
                .catch(error => reject(error));
        });
    }

    /**
     * Guarda un tablero
     * @param {Object} dashboard - Tablero a guardar
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve cuando el tablero se ha guardado
     */
    saveDashboard(dashboard, companyId) {
        return new Promise((resolve, reject) => {
            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData) {
                        reject(new Error(`La empresa con ID ${companyId} no existe`));
                        return;
                    }

                    if (!companyData.tableros) {
                        companyData.tableros = [];
                    }

                    // Buscar si el tablero ya existe
                    const index = companyData.tableros.findIndex(d => d.id === dashboard.id);

                    if (index >= 0) {
                        // Actualizar tablero existente
                        companyData.tableros[index] = dashboard;
                    } else {
                        // Añadir nuevo tablero
                        companyData.tableros.push(dashboard);
                    }

                    return this.saveCompanyData(companyId, companyData);
                })
                .then(() => resolve())
                .catch(error => reject(error));
        });
    }

    /**
     * Elimina un tablero
     * @param {number} dashboardId - ID del tablero
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con true si se eliminó correctamente
     */
    deleteDashboard(dashboardId, companyId) {
        return new Promise((resolve, reject) => {
            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData || !companyData.tableros) {
                        resolve(false);
                        return;
                    }

                    const initialLength = companyData.tableros.length;
                    companyData.tableros = companyData.tableros.filter(dashboard => dashboard.id !== dashboardId);

                    // Si era el tablero actual, establecer otro como actual
                    if (companyData.tableroActual === dashboardId && companyData.tableros.length > 0) {
                        companyData.tableroActual = companyData.tableros[0].id;
                    } else if (companyData.tableros.length === 0) {
                        companyData.tableroActual = null;
                    }

                    if (companyData.tableros.length !== initialLength) {
                        return this.saveCompanyData(companyId, companyData)
                            .then(() => resolve(true));
                    } else {
                        resolve(false);
                    }
                })
                .catch(error => reject(error));
        });
    }

    /**
     * Obtiene el tema actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @returns {Promise} - Promesa que se resuelve con el nombre del tema actual
     */
    getCurrentTheme(companyId) {
        return this.getCompanyData(companyId)
            .then(companyData => companyData && companyData.temaActual ? companyData.temaActual : 'tron');
    }

    /**
     * Establece el tema actual de una empresa
     * @param {number} companyId - ID de la empresa
     * @param {string} theme - Nombre del tema
     * @returns {Promise} - Promesa que se resuelve cuando se ha establecido el tema actual
     */
    setCurrentTheme(companyId, theme) {
        return new Promise((resolve, reject) => {
            this.getCompanyData(companyId)
                .then(companyData => {
                    if (!companyData) {
                        reject(new Error(`La empresa con ID ${companyId} no existe`));
                        return;
                    }

                    companyData.temaActual = theme;
                    return this.saveCompanyData(companyId, companyData);
                })
                .then(() => resolve())
                .catch(error => reject(error));
        });
    }

    /**
     * Inicializa la base de datos con datos de prueba si está vacía
     * @returns {Promise} - Promesa que se resuelve cuando se han inicializado los datos de prueba
     */
    initializetestdata() {
        return new Promise((resolve, reject) => {
            // Verificar si la base de datos ya tiene datos
            if (Object.keys(this.db).length > 0) {
                resolve();
                return;
            }

            console.log('Inicializando datos de prueba...');

            // Crear empresa por defecto (id=1, nombre=iPRA, activo=true)
            const defaultCompany = {
                id: 1,
                nombre: 'iPRA',
                activo: true
            };

            // Crear usuario administrador (login=admin, clave=super)
            const adminUser = {
                id: 1,
                login: 'admin',
                nombre: 'Administrador',
                clave: 'super',
                telefono: '',
                email: '<EMAIL>',
                tipo: 'admin',
                empresaId: 1
            };

            // Crear usuario normal (login=usu1, clave=usu1)
            const normalUser = {
                id: 2,
                login: 'usu1',
                nombre: 'Usuario Normal',
                clave: 'usu1',
                telefono: '',
                email: '<EMAIL>',
                tipo: 'usuario',
                empresaId: 1
            };

            // Crear tablero 800x600px para la empresa por defecto
            const defaultDashboard = {
                id: 1,
                name: 'Tablero principal',
                width: 800,
                height: 600,
                backgroundColor: '#ffffff',
                showWidgetBorders: true,
                transparentWidgets: false,
                showGrid: true,
                gridColor: '#1a3a5a',
                widgetCount: 0,
                theme: 'tron',
                widgets: [],
                userId: adminUser.id,
                userName: adminUser.nombre
            };

            // Crear segunda empresa (id=2, nombre=empresa2, activo=true)
            const secondCompany = {
                id: 2,
                nombre: 'empresa2',
                activo: true
            };

            // Crear usuario administrador para la empresa 2 (login=admin2, clave=super2)
            const admin2User = {
                id: 3,
                login: 'admin2',
                nombre: 'Administrador Empresa 2',
                clave: 'super2',
                telefono: '',
                email: '<EMAIL>',
                tipo: 'admin',
                empresaId: 2
            };

            // Crear tablero 800x600px para la empresa 2
            const secondDashboard = {
                id: 2,
                name: 'tablero empresa 2',
                width: 800,
                height: 600,
                backgroundColor: '#ffffff',
                showWidgetBorders: true,
                transparentWidgets: false,
                showGrid: true,
                gridColor: '#1a3a5a',
                widgetCount: 0,
                theme: 'tron',
                widgets: [],
                userId: admin2User.id,
                userName: admin2User.nombre
            };

            // Crear usuario normal para la empresa 2 (login=usu2, clave=usu2)
            const normal2User = {
                id: 4,
                login: 'usu2',
                nombre: 'Usuario Normal Empresa 2',
                clave: 'usu2',
                telefono: '',
                email: '<EMAIL>',
                tipo: 'usuario',
                empresaId: 2
            };

            // Función para generar un nombre de empresa aleatorio con sentido
            const generateCompanyName = () => {
                // Prefijos para empresas tecnológicas
                const techPrefixes = ['Tech', 'Cyber', 'Digital', 'Smart', 'Intelligent', 'Advanced', 'Future', 'Next', 'Modern', 'Dynamic', 'Quantum', 'Nano', 'Micro', 'Macro', 'Giga', 'Meta', 'Virtual', 'Cloud', 'Data', 'Info'];
                const techRoots = ['Soft', 'Tech', 'Systems', 'Solutions', 'Dynamics', 'Logic', 'Data', 'Info', 'Net', 'Web', 'Cloud', 'Mobile', 'Vision', 'Mind', 'Think', 'Idea', 'Concept', 'Innovation', 'Creation', 'Development'];
                const techSuffixes = ['Corp', 'Inc', 'Ltd', 'Group', 'Systems', 'Solutions', 'Technologies', 'Enterprises', 'Partners', 'Associates', 'International', 'Global', 'World', 'Industries', 'Services', 'Consulting', 'Software', 'Hardware', 'Networks', 'Communications'];

                // Nombres para empresas industriales
                const industrialNames = ['Aceros', 'Hierros', 'Metales', 'Construcciones', 'Materiales', 'Cementos', 'Hormigones', 'Estructuras', 'Maquinaria', 'Equipos', 'Herramientas', 'Montajes', 'Instalaciones', 'Mantenimiento', 'Reparaciones', 'Fabricaciones', 'Industrias', 'Manufacturas', 'Talleres', 'Fundiciones'];
                const industrialSuffixes = ['Industrial', 'Mecánica', 'Técnica', 'Metalúrgica', 'Constructora', 'Ingeniería', 'Montajes', 'Estructuras', 'Maquinaria', 'Equipos', 'Herramientas', 'Instalaciones', 'Mantenimiento', 'Reparaciones', 'Fabricaciones', 'Industrias', 'Manufacturas', 'Talleres', 'Fundiciones', 'Soldaduras'];

                // Nombres para empresas de servicios
                const serviceNames = ['Asesoría', 'Consultoría', 'Gestión', 'Administración', 'Servicios', 'Soluciones', 'Atención', 'Asistencia', 'Ayuda', 'Soporte', 'Apoyo', 'Orientación', 'Dirección', 'Planificación', 'Organización', 'Coordinación', 'Supervisión', 'Control', 'Evaluación', 'Seguimiento'];
                const serviceAreas = ['Fiscal', 'Contable', 'Laboral', 'Jurídica', 'Legal', 'Financiera', 'Económica', 'Empresarial', 'Comercial', 'Marketing', 'Publicidad', 'Comunicación', 'Recursos Humanos', 'Personal', 'Formación', 'Capacitación', 'Desarrollo', 'Innovación', 'Calidad', 'Medioambiental'];

                // Nombres para empresas comerciales
                const commercialPrefixes = ['Comercial', 'Distribuciones', 'Almacenes', 'Tiendas', 'Supermercados', 'Hipermercados', 'Centros', 'Grandes', 'Pequeños', 'Medianos', 'Exclusivos', 'Selectos', 'Especializados', 'Generales', 'Mayoristas', 'Minoristas', 'Importaciones', 'Exportaciones', 'Representaciones', 'Delegaciones'];
                const commercialProducts = ['Alimentación', 'Bebidas', 'Textil', 'Ropa', 'Calzado', 'Muebles', 'Decoración', 'Hogar', 'Electrodomésticos', 'Electrónica', 'Informática', 'Telefonía', 'Papelería', 'Librería', 'Juguetes', 'Deportes', 'Ferretería', 'Bricolaje', 'Jardinería', 'Mascotas'];

                // Nombres para empresas de construcción
                const constructionPrefixes = ['Construcciones', 'Obras', 'Edificaciones', 'Promociones', 'Proyectos', 'Desarrollos', 'Rehabilitaciones', 'Reformas', 'Instalaciones', 'Estructuras', 'Cerramientos', 'Cubiertas', 'Fachadas', 'Pavimentos', 'Revestimientos', 'Aislamientos', 'Impermeabilizaciones', 'Canalizaciones', 'Excavaciones', 'Movimientos'];
                const constructionSuffixes = ['Constructora', 'Inmobiliaria', 'Promotora', 'Urbanizadora', 'Edificadora', 'Rehabilitadora', 'Reformadora', 'Instaladora', 'Estructural', 'Arquitectónica', 'Técnica', 'Especializada', 'Integral', 'General', 'Urbana', 'Rural', 'Residencial', 'Industrial', 'Comercial', 'Pública'];

                // Nombres para empresas de alimentación
                const foodPrefixes = ['Alimentación', 'Productos', 'Distribuciones', 'Elaborados', 'Conservas', 'Congelados', 'Precocinados', 'Delicatessen', 'Gourmet', 'Selectos', 'Tradicionales', 'Artesanos', 'Naturales', 'Ecológicos', 'Biológicos', 'Orgánicos', 'Dietéticos', 'Saludables', 'Especiales', 'Exclusivos'];
                const foodProducts = ['Alimenticios', 'Cárnicos', 'Lácteos', 'Pesqueros', 'Agrícolas', 'Hortofrutícolas', 'Panadería', 'Pastelería', 'Repostería', 'Confitería', 'Chocolatería', 'Heladería', 'Cafetería', 'Bebidas', 'Vinos', 'Licores', 'Aceites', 'Conservas', 'Especias', 'Condimentos'];

                // Nombres para empresas de transporte
                const transportPrefixes = ['Transportes', 'Mudanzas', 'Mensajería', 'Paquetería', 'Logística', 'Distribución', 'Reparto', 'Envíos', 'Traslados', 'Portes', 'Fletes', 'Acarreos', 'Cargas', 'Descargas', 'Almacenaje', 'Depósito', 'Custodia', 'Guardamuebles', 'Garaje', 'Aparcamiento'];
                const transportSuffixes = ['Express', 'Rápido', 'Urgente', 'Inmediato', 'Directo', 'Puerta a Puerta', 'Nacional', 'Internacional', 'Europeo', 'Mundial', 'Global', 'Universal', 'Total', 'Integral', 'Completo', 'Seguro', 'Fiable', 'Puntual', 'Económico', 'Barato'];

                // Nombres para empresas de hostelería
                const hospitalityPrefixes = ['Hotel', 'Hostal', 'Pensión', 'Albergue', 'Residencia', 'Apartamentos', 'Camping', 'Bungalows', 'Cabañas', 'Casas', 'Villas', 'Chalets', 'Restaurante', 'Bar', 'Cafetería', 'Pub', 'Discoteca', 'Sala', 'Club', 'Salón'];
                const hospitalitySuffixes = ['Palace', 'Luxury', 'Confort', 'Relax', 'Descanso', 'Vacaciones', 'Ocio', 'Tiempo Libre', 'Turismo', 'Viajes', 'Excursiones', 'Aventura', 'Naturaleza', 'Rural', 'Urbano', 'Playa', 'Montaña', 'Sierra', 'Valle', 'Río'];

                // Nombres para empresas de salud
                const healthPrefixes = ['Centro', 'Clínica', 'Hospital', 'Consultorio', 'Gabinete', 'Instituto', 'Laboratorio', 'Farmacia', 'Óptica', 'Ortopedia', 'Fisioterapia', 'Rehabilitación', 'Psicología', 'Psiquiatría', 'Odontología', 'Dental', 'Médico', 'Sanitario', 'Salud', 'Bienestar'];
                const healthSuffixes = ['Médico', 'Clínico', 'Hospitalario', 'Sanitario', 'Salud', 'Bienestar', 'Vida', 'Vital', 'Saludable', 'Sano', 'Natural', 'Alternativo', 'Complementario', 'Integral', 'Holístico', 'Preventivo', 'Curativo', 'Terapéutico', 'Asistencial', 'Cuidados'];

                // Elegir un tipo de empresa al azar
                const companyTypes = [{
                        prefixes: techPrefixes,
                        roots: techRoots,
                        suffixes: techSuffixes,
                        format: (p, r, s) => `${p}${r} ${s}`
                    },
                    {
                        prefixes: industrialNames,
                        suffixes: industrialSuffixes,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: serviceNames,
                        suffixes: serviceAreas,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: commercialPrefixes,
                        suffixes: commercialProducts,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: constructionPrefixes,
                        suffixes: constructionSuffixes,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: foodPrefixes,
                        suffixes: foodProducts,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: transportPrefixes,
                        suffixes: transportSuffixes,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: hospitalityPrefixes,
                        suffixes: hospitalitySuffixes,
                        format: (p, s) => `${p} ${s}`
                    },
                    {
                        prefixes: healthPrefixes,
                        suffixes: healthSuffixes,
                        format: (p, s) => `${p} ${s}`
                    }
                ];

                const companyType = companyTypes[Math.floor(Math.random() * companyTypes.length)];

                if (companyType.roots) {
                    const prefix = companyType.prefixes[Math.floor(Math.random() * companyType.prefixes.length)];
                    const root = companyType.roots[Math.floor(Math.random() * companyType.roots.length)];
                    const suffix = companyType.suffixes[Math.floor(Math.random() * companyType.suffixes.length)];
                    return companyType.format(prefix, root, suffix);
                } else {
                    const prefix = companyType.prefixes[Math.floor(Math.random() * companyType.prefixes.length)];
                    const suffix = companyType.suffixes[Math.floor(Math.random() * companyType.suffixes.length)];
                    return companyType.format(prefix, suffix);
                }
            };

            // Guardar las dos primeras empresas y sus usuarios
            let promiseChain = this.saveCompany(defaultCompany)
                .then(() => this.saveUser(adminUser))
                .then(() => this.saveUser(normalUser))
                .then(() => this.saveDashboard(defaultDashboard, 1))
                .then(() => this.setCurrentDashboardId(1, 1))
                .then(() => this.saveCompany(secondCompany))
                .then(() => this.saveDashboard(secondDashboard, 2))
                .then(() => this.setCurrentDashboardId(2, 2))
                .then(() => this.saveUser(admin2User))
                .then(() => this.saveUser(normal2User));

            // Generar 498 empresas adicionales (para llegar a 500 en total)
            let nextUserId = 5; // Empezamos desde el ID 5 para usuarios
            let nextDashboardId = 3; // Empezamos desde el ID 3 para tableros

            for (let i = 3; i <= 500; i++) {
                const companyId = i;
                const companyName = generateCompanyName();

                // Crear empresa
                const company = {
                    id: companyId,
                    nombre: companyName,
                    activo: Math.random() > 0.2 // 80% de probabilidad de estar activo
                };

                // Crear usuario para esta empresa
                const user = {
                    id: nextUserId++,
                    login: `usu${companyId}`,
                    nombre: `Usuario de ${companyName}`,
                    clave: '1234',
                    telefono: '',
                    email: `usuario@${companyName.toLowerCase().replace(/[^a-z0-9]/g, '')}.com`,
                    tipo: 'usuario',
                    empresaId: companyId
                };

                // Crear tablero para esta empresa
                const dashboard = {
                    id: nextDashboardId++,
                    name: `Tablero de ${companyName}`,
                    width: 800,
                    height: 600,
                    backgroundColor: '#ffffff',
                    showWidgetBorders: true,
                    transparentWidgets: false,
                    showGrid: true,
                    gridColor: '#1a3a5a',
                    widgetCount: 0,
                    theme: 'tron',
                    widgets: [],
                    userId: user.id,
                    userName: user.nombre
                };

                // Añadir a la cadena de promesas
                promiseChain = promiseChain
                    .then(() => this.saveCompany(company))
                    .then(() => this.saveUser(user))
                    .then(() => this.saveDashboard(dashboard, companyId))
                    .then(() => this.setCurrentDashboardId(companyId, dashboard.id));
            }

            // Finalizar la cadena de promesas
            promiseChain
                .then(() => {
                    console.log('Datos de prueba inicializados correctamente (500 empresas)');
                    resolve();
                })
                .catch(error => {
                    console.error('Error al inicializar datos de prueba:', error);
                    reject(error);
                });
        });
    }
}

// Crear instancia del gestor de base de datos
const dbManager = new DatabaseManager();