/**
 * Manejador específico para la entidad de tableros
 */
class TablerosEntityHandler {
    /**
     * Obtiene la configuración específica para la entidad de tableros
     * @param {boolean} skipTitleCustomization - Si es true, no personaliza el título (para evitar recursión)
     * @returns {Object} - Configuración de la entidad
     */
    getConfig(skipTitleCustomization = false) {
        // Título por defecto
        let title = 'Tableros';

        // Personalizar el título solo si no se está evitando la recursión
        if (!skipTitleCustomization) {
            // Verificar si estamos mostrando tableros de una empresa específica
            const entityManager = app.entityManagers.find(em => em.entityType === 'tableros');
            const specificCompanyId = entityManager && entityManager.params && entityManager.params.companyId;

            if (specificCompanyId) {
                // Obtener el nombre de la empresa para personalizar el título
                dbManager.getCompany(specificCompanyId)
                    .then(company => {
                        if (company) {
                            // Actualizar el título en la UI
                            const titleElement = document.querySelector('.entity-title');
                            if (titleElement) {
                                titleElement.textContent = `Tableros de ${company.nombre}`;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error al obtener empresa para personalizar título:', error);
                    });
            }
        }

        return {
            title: title,
            fields: [{
                    name: 'id',
                    label: 'ID',
                    type: 'number',
                    sortable: true,
                    visible: true
                },
                {
                    name: 'name',
                    label: 'Nombre',
                    type: 'text',
                    sortable: true,
                    visible: true,
                    required: true
                },
                {
                    name: 'userName',
                    label: 'Usuario',
                    type: 'text',
                    sortable: true,
                    visible: true,
                    editable: false
                },
                {
                    name: 'empresaId',
                    label: 'ID Empresa',
                    type: 'number',
                    sortable: true,
                    visible: false, // Campo oculto en el listado
                    editable: false // No aparece en el formulario de edición
                },
                {
                    name: 'observ',
                    label: 'Observaciones',
                    type: 'textarea',
                    sortable: true,
                    visible: true
                },
                {
                    name: 'activo',
                    label: 'Activo',
                    type: 'checkbox',
                    sortable: true,
                    visible: true
                }
            ]
        };
    }

    /**
     * Carga los tableros desde la base de datos
     * @param {EntityManager} entityManager - Instancia del gestor de entidades
     * @returns {Promise<Array>} - Promesa que se resuelve con la lista de tableros
     */
    loadEntities(entityManager) {
        return new Promise((resolve, reject) => {
            // Determinar qué tableros cargar según el tipo de usuario actual
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            if (!currentUser) {
                // Si no hay usuario autenticado, redirigir a login
                authManager.logout();
                // Resolver con array vacío para evitar errores en la UI
                resolve([]);
                return;
            }

            // Obtener el ID de empresa de los parámetros o usar el del usuario actual
            const specificCompanyId = entityManager && entityManager.params && entityManager.params.companyId;
            const specificUserId = entityManager && entityManager.params && entityManager.params.userId;
            const companyIdToUse = specificCompanyId || currentCompanyId;

            if (!companyIdToUse) {
                // Mostrar mensaje de error si no hay ID de empresa
                dialogManager.alert('No se ha especificado una empresa para mostrar los tableros')
                    .then(() => {
                        // Volver al menú principal
                        navigateTo('main-menu');
                    });
                resolve([]);
                return;
            }

            // Verificar permisos: solo puede ver tableros de su empresa a menos que sea admin de empresa 1
            if (specificCompanyId && specificCompanyId !== currentCompanyId &&
                !(currentUser.tipo === 'admin' && currentCompanyId === 1)) {
                const errorMsg = 'No tiene permisos para ver tableros de esta empresa';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg)
                    .then(() => {
                        // Volver al menú principal
                        navigateTo('main-menu');
                    });
                resolve([]);
                return;
            }

            // Cargar los tableros de la empresa, filtrando por usuario si se especificó
            dbManager.getDashboards(companyIdToUse, specificUserId)
                .then(dashboards => {
                    // Asegurarse de que cada tablero tenga el ID de empresa
                    const dashboardsWithCompanyId = dashboards.map(dashboard => ({
                        ...dashboard,
                        empresaId: companyIdToUse
                    }));

                    // Actualizar el título según el contexto
                    if (entityManager) {
                        if (specificUserId) {
                            // Si se especificó un usuario, buscar su nombre para el título
                            dbManager.getUsers(companyIdToUse)
                                .then(users => {
                                    const user = users.find(u => u.id === specificUserId);
                                    if (user) {
                                        const title = `Tableros de ${user.nombre}`;
                                        entityManager.updateTitle(title);
                                    }
                                })
                                .catch(error => {
                                    console.error('Error al obtener usuario:', error);
                                });
                        } else if (specificCompanyId) {
                            // Si solo se especificó una empresa, usar su nombre para el título
                            dbManager.getCompany(specificCompanyId)
                                .then(company => {
                                    if (company) {
                                        const title = `Tableros de ${company.nombre}`;
                                        entityManager.updateTitle(title);
                                    }
                                })
                                .catch(error => {
                                    console.error('Error al obtener empresa:', error);
                                });
                        }
                    }

                    resolve(dashboardsWithCompanyId);
                })
                .catch(error => {
                    console.error('Error al cargar tableros:', error);
                    showNotification(`Error al cargar tableros: ${error.message}`, 'error');
                    reject(error);
                });
        });
    }

    /**
     * Guarda un tablero en la base de datos
     * @param {Object} dashboardData - Datos del tablero a guardar
     * @returns {Promise} - Promesa que se resuelve cuando el tablero se ha guardado
     */
    saveEntity(dashboardData) {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores y editores pueden crear/editar tableros
            if (currentUser.tipo !== 'admin' && currentUser.tipo !== 'editor') {
                const errorMsg = 'No tiene permisos para realizar esta acción';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg);
                reject(new Error(errorMsg));
                return;
            }

            // Obtener el ID de empresa de los parámetros
            const entityManager = app.entityManagers.find(em => em.entityType === 'tableros');
            let specificCompanyId = entityManager && entityManager.params && entityManager.params.companyId;

            // Asignar el ID de empresa desde los parámetros o usar el del usuario actual
            let companyIdToUse = specificCompanyId || currentCompanyId;

            if (dashboardData._entity && dashboardData._entity.empresaId) {
                companyIdToUse = dashboardData._entity.empresaId;
                specificCompanyId = companyIdToUse;
            }

            // Solo los administradores de la empresa 1 pueden crear tableros para otras empresas
            if (specificCompanyId && specificCompanyId !== currentCompanyId &&
                !(currentUser.tipo === 'admin' && currentCompanyId === 1)) {
                const errorMsg = 'No tiene permisos para crear tableros en esta empresa';
                dialogManager.alert(errorMsg);
                reject(new Error(errorMsg));
                return;
            }

            // Asignar el ID de empresa al tablero
            dashboardData.empresaId = companyIdToUse;

            // Asegurarse de que el ID del tablero sea un número si existe
            if (dashboardData.id) {
                dashboardData.id = parseInt(dashboardData.id, 10);
            }

            // Si es un nuevo tablero, asignar ID y valores por defecto
            if (!dashboardData.id) {
                // Obtener todos los tableros para determinar el siguiente ID
                dbManager.getDashboards(companyIdToUse)
                    .then(dashboards => {
                        const maxId = dashboards.reduce((max, dashboard) => Math.max(max, dashboard.id), 0);
                        dashboardData.id = maxId + 1;

                        // Obtener el usuario actual
                        const currentUser = authManager.user;

                        // Determinar el usuario al que asociar el tablero
                        // Si estamos viendo los tableros de un usuario específico, asociar el tablero a ese usuario
                        const entityManager = app.entityManagers.find(em => em.entityType === 'tableros');
                        const specificUserId = entityManager && entityManager.params && entityManager.params.userId;

                        // Determinar el ID y nombre del usuario para el tablero
                        let userIdToUse = currentUser.id;
                        let userNameToUse = currentUser.nombre;

                        // Si hay un ID de usuario específico, usar ese usuario
                        if (specificUserId) {
                            userIdToUse = specificUserId;
                            // Intentar obtener el nombre del usuario
                            return dbManager.getUsers(companyIdToUse)
                                .then(users => {
                                    const user = users.find(u => u.id === specificUserId);
                                    if (user) {
                                        userNameToUse = user.nombre;
                                    }

                                    // Crear un tablero básico con los valores por defecto
                                    const newDashboard = {
                                        ...dashboardData,
                                        width: 800,
                                        height: 600,
                                        backgroundColor: '#ffffff',
                                        showWidgetBorders: true,
                                        transparentWidgets: false,
                                        showGrid: true,
                                        gridColor: '#1a3a5a',
                                        widgetCount: 0,
                                        theme: themeManager.getCurrentTheme(),
                                        widgets: [],
                                        // Valores por defecto para los nuevos campos
                                        observ: dashboardData.observ || '',
                                        activo: dashboardData.activo !== undefined ? dashboardData.activo : true,
                                        // Asociar el tablero al usuario específico o al usuario actual
                                        userId: userIdToUse,
                                        userName: userNameToUse
                                    };

                                    return dbManager.saveDashboard(newDashboard, companyIdToUse);
                                });
                        }

                        // Si no hay usuario específico, crear el tablero con el usuario actual
                        const newDashboard = {
                            ...dashboardData,
                            width: 800,
                            height: 600,
                            backgroundColor: '#ffffff',
                            showWidgetBorders: true,
                            transparentWidgets: false,
                            showGrid: true,
                            gridColor: '#1a3a5a',
                            widgetCount: 0,
                            theme: themeManager.getCurrentTheme(),
                            widgets: [],
                            // Valores por defecto para los nuevos campos
                            observ: dashboardData.observ || '',
                            activo: dashboardData.activo !== undefined ? dashboardData.activo : true,
                            // Asociar el tablero al usuario actual
                            userId: userIdToUse,
                            userName: userNameToUse
                        };

                        return dbManager.saveDashboard(newDashboard, companyIdToUse);
                    })
                    .then(() => {
                        // Cerrar el formulario de edición
                        const entityManager = app.entityManagers.find(em => em.entityType === 'tableros');
                        if (entityManager) {
                            entityManager.closeEntityForm();
                        }

                        // Después de crear el tablero, abrir la pantalla de tablero
                        this.openDashboard(dashboardData);
                        resolve();
                    })
                    .catch(error => reject(error));
            } else {
                // Actualizar tablero existente
                console.log(`Actualizando tablero ID: ${dashboardData.id} para empresa ID: ${companyIdToUse}`);

                // Asegurarse de que el ID del tablero sea un número
                const dashboardId = parseInt(dashboardData.id, 10);

                dbManager.getDashboards(companyIdToUse)
                    .then(dashboards => {
                        console.log(`Tableros encontrados para empresa ${companyIdToUse}:`, dashboards.map(d => d.id));

                        // Buscar el tablero existente usando comparación numérica
                        const existingDashboard = dashboards.find(d => d.id === dashboardId);
                        if (!existingDashboard) {
                            console.error(`No se encontró el tablero con ID ${dashboardId} en la empresa ${companyIdToUse}`);
                            throw new Error(`No se encontró el tablero con ID ${dashboardId}`);
                        }

                        console.log(`Tablero encontrado:`, existingDashboard);

                        // Actualizar los campos editables, manteniendo el resto de propiedades
                        const updatedDashboard = {
                            ...existingDashboard,
                            name: dashboardData.name,
                            observ: dashboardData.observ || existingDashboard.observ || '',
                            activo: dashboardData.activo !== undefined ? dashboardData.activo : (existingDashboard.activo !== undefined ? existingDashboard.activo : true)
                        };

                        return dbManager.saveDashboard(updatedDashboard, companyIdToUse);
                    })
                    .then(() => resolve())
                    .catch(error => reject(error));
            }
        });
    }

    /**
     * Elimina un tablero de la base de datos
     * @param {number} dashboardId - ID del tablero a eliminar
     * @returns {Promise} - Promesa que se resuelve cuando el tablero se ha eliminado
     */
    deleteEntity(dashboardId) {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores y editores pueden eliminar tableros
            if (currentUser.tipo !== 'admin' && currentUser.tipo !== 'editor') {
                const errorMsg = 'No tiene permisos para realizar esta acción';
                // Mostrar diálogo de error
                dialogManager.alert(errorMsg);
                reject(new Error(errorMsg));
                return;
            }

            // Obtener el ID de empresa de los parámetros
            const entityManager = app.entityManagers.find(em => em.entityType === 'tableros');
            const specificCompanyId = entityManager && entityManager.params && entityManager.params.companyId;

            // Asignar el ID de empresa desde los parámetros o usar el del usuario actual
            const companyIdToUse = specificCompanyId || currentCompanyId;

            // Asegurarse de que el ID del tablero sea un número
            const dashboardIdNum = parseInt(dashboardId, 10);
            console.log(`Eliminando tablero ID: ${dashboardIdNum} para empresa ID: ${companyIdToUse}`);

            // Eliminar el tablero
            dbManager.deleteDashboard(dashboardIdNum, companyIdToUse)
                .then(success => {
                    if (success) {
                        resolve();
                    } else {
                        reject(new Error('No se pudo eliminar el tablero'));
                    }
                })
                .catch(error => reject(error));
        });
    }

    /**
     * Abre un tablero en la pantalla de tablero
     * @param {Object} dashboard - El tablero a abrir
     */
    openDashboard(dashboard) {
        // Asegurarse de que el ID del tablero sea un número
        if (dashboard && dashboard.id) {
            dashboard.id = parseInt(dashboard.id, 10);
        }

        console.log(`Abriendo tablero:`, dashboard);

        // Guardar el hash actual para poder volver a él
        const currentHash = window.location.hash.substring(1);
        console.log(`Hash actual antes de abrir tablero: ${currentHash}`);

        // Si el hash actual es 'usuarios', guardar este estado para poder volver correctamente
        if (currentHash.startsWith('usuarios')) {
            // Guardar en sessionStorage para recuperarlo al volver atrás
            sessionStorage.setItem('previousHash', currentHash);
            console.log(`Guardando hash anterior: ${currentHash}`);
        }

        // Cerrar todos los contenedores abiertos antes de abrir el tablero
        // Esto asegura que no haya elementos con pointer-events: none que interfieran
        const currentElement = peekUIStack();
        if (currentElement && currentElement.element) {
            // Cerrar el contenedor actual sin animación para evitar problemas
            currentElement.element.classList.add('hidden');
            popFromUIStack(true);
        }

        // Limpiar cualquier clase que pueda interferir con los eventos del ratón
        document.body.classList.remove('selection-mode');

        // Actualizar el hash y abrir el tablero
        updateUrlHash('dashboard');
        window.dashboardManager.useDashboard(dashboard);
    }

    /**
     * Obtiene las opciones de menú específicas para las filas de la tabla de tableros
     * @param {Object} entity - La entidad (tablero) sobre la que se mostrarán las opciones
     * @returns {Array} - Opciones de menú para la fila
     */
    getRowMenuOptions(entity) {
        const options = [];

        // Opción de editar tablero (abrir en pantalla de tablero)
        options.push({
            label: 'Editar tablero',
            action: () => this.openDashboard(entity)
        });

        return options;
    }

    /**
     * Maneja el evento de doble clic en una fila de la tabla
     * @param {Object} entity - La entidad (tablero) sobre la que se hizo doble clic
     */
    handleRowDoubleClick(entity) {
        this.openDashboard(entity);
    }
}

// Registrar el manejador de tableros en el gestor de entidades
app['entityManager'].registerEntityHandler('tableros', new TablerosEntityHandler());