/**
 * Módulo de navegación para iPRA
 * Implementa la navegación entre pantallas utilizando delegación de eventos
 */
function setupNavigation() {
    // Manejar el botón de menú principal en el dashboard
    on(document, 'click', '#main-menu-btn,#entity-back-menu-btn', function () {
        // Navegar al menú principal
        navigateTo('main-menu');
    });

    // Añadir la función "back" a app.acciones para volver a la pantalla anterior
    app.acciones.back = function () {
        console.log('Acción back: Volviendo a la pantalla anterior');

        // Obtener el hash actual
        const currentHash = window.location.hash.substring(1);

        // Si estamos en un tablero, verificar si hay un hash anterior guardado
        if (currentHash === 'tablero') {
            const previousHash = sessionStorage.getItem('previousHash');

            if (previousHash && previousHash.startsWith('usuarios')) {
                console.log(`Acción back: Volviendo a hash guardado: ${previousHash}`);
                // Limpiar el hash guardado
                sessionStorage.removeItem('previousHash');

                // Navegar directamente a la pantalla de usuarios
                navigateToHash('usuarios');
            } else {
                console.log('Acción back: Estamos en un tablero, navegando a tableros');

                // Navegar directamente a la pantalla de tableros
                navigateToHash('tableros');
            }
        } else {
            // Para otros casos, usar el comportamiento estándar
            window.history.back();
        }
    };

    // Ir al menú principal
    app.acciones.inicio = function () {
        navigateTo('main-menu');
    }
}