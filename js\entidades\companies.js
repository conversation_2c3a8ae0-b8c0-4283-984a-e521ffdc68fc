/**
 * Manejador específico para la entidad de empresas
 */
class CompanyEntityHandler {
    /**
     * Verifica si el usuario tiene privilegios para realizar una acción específica
     * @param {string} permiso - Tipo de permiso a verificar ('addRecord', 'deleteRecord', 'showHamburgerMenu', etc.)
     * @returns {boolean} - true si el usuario tiene privilegios, false en caso contrario
     */
    verifPrivilegies(permiso) {
        // Obtener información del usuario actual
        const currentUser = authManager.user;
        const currentCompanyId = authManager.companyId;

        // Verificar si el usuario es administrador en la empresa 1
        const isAdminCompany1 = currentUser && currentUser.tipo === 'admin' && currentCompanyId === 1;

        // Verificar el tipo de permiso solicitado
        switch (permiso) {
            case 'addRecord':
            case 'deleteRecord':
            case 'showHamburgerMenu':
                return isAdminCompany1;
                // Se pueden añadir más casos específicos en el futuro
            default:
                return isAdminCompany1; // Por defecto, solo admin de empresa 1
        }
    }

    /**
     * Obtiene la configuración específica para la entidad de empresas
     * @returns {Object} - Configuración de la entidad
     */
    getConfig() {
        return {
            title: 'Empresas',
            fields: [{
                    name: 'id',
                    label: 'ID',
                    type: 'number',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'nombre',
                    label: 'Nombre',
                    type: 'text',
                    sortable: true,
                    visible: true,
                    required: true
                },
                {
                    name: 'direccion',
                    label: 'Dirección',
                    type: 'text',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'ciudad',
                    label: 'Ciudad',
                    type: 'text',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'telefono',
                    label: 'Teléfono',
                    type: 'text',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'email',
                    label: 'Email',
                    type: 'email',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'nombreContacto',
                    label: 'Nombre Contacto',
                    type: 'text',
                    sortable: true,
                    visible: false
                },
                {
                    name: 'activo',
                    label: 'Activo',
                    type: 'checkbox',
                    sortable: true,
                    visible: false
                }
            ],
            actions: ['edit', 'delete']
        };
    }

    /**
     * Obtiene las opciones de menú específicas para la tabla de empresas
     * @returns {Array} - Opciones de menú
     */
    getTableMenuOptions() {
        return [{
            label: 'Generar datos de prueba',
            action: () => this.generateTestData()
        }];
    }

    /**
     * Obtiene las opciones de menú específicas para las filas de la tabla de empresas
     * @param {Object} entity - La entidad (empresa) sobre la que se mostrarán las opciones
     * @returns {Array} - Opciones de menú para la fila
     */
    getRowMenuOptions(entity) {
        return [{
                label: 'Ver usuarios',
                action: () => this.showCompanyUsers(entity)
            },
            {
                label: 'Ver tableros',
                action: () => this.showCompanyDashboards(entity)
            }
        ];
    }

    /**
     * Muestra los usuarios de una empresa
     * @param {Object} company - La empresa cuyos usuarios se mostrarán
     */
    showCompanyUsers(company) {
        // Crear una nueva instancia de EntityManager para mostrar los usuarios
        const usersEntityManager = new EntityManager();

        // Inicializar el gestor con el tipo de entidad 'usuarios', el ID de la empresa y el nombre
        usersEntityManager.init('usuarios', {
                companyId: company.id,
                companyName: company.nombre // Incluir el nombre de la empresa para el hash
            })
            .then(entityManagerInstance => {
                // Una vez cargados los datos, mostrar la interfaz
                entityManagerInstance.show();
            })
            .catch(error => {
                console.error(`Error al inicializar el gestor de usuarios:`, error);
                showNotification(`Error al cargar usuarios: ${error.message}`, 'error');
            });
    }

    /**
     * Muestra los tableros de una empresa
     * @param {Object} company - La empresa cuyos tableros se mostrarán
     */
    showCompanyDashboards(company) {
        // Restablecer el usuario actual en dashboardManager al usuario de la aplicación
        // para que los nuevos tableros se asocien al usuario actual
        const currentUser = authManager.user;
        dashboardManager.currentUserId = currentUser.id;
        dashboardManager.currentUserName = currentUser.nombre;

        console.log(`Estableciendo usuario para nuevos tableros: ${currentUser.id} (${currentUser.nombre})`);

        // Crear una nueva instancia de EntityManager para mostrar los tableros
        const dashboardsEntityManager = new EntityManager();

        // Inicializar el gestor con el tipo de entidad 'tableros' y el ID de la empresa
        dashboardsEntityManager.init('tableros', {
                companyId: company.id
            })
            .then(entityManagerInstance => {
                // Una vez cargados los datos, mostrar la interfaz
                entityManagerInstance.show();
            })
            .catch(error => {
                console.error(`Error al inicializar el gestor de tableros:`, error);
                showNotification(`Error al cargar tableros: ${error.message}`, 'error');
            });
    }

    /**
     * Maneja el evento de doble clic en una fila de la tabla
     * @param {Object} entity - La empresa sobre la que se hizo doble clic
     */
    handleRowDoubleClick(entity) {
        // Al hacer doble clic en una empresa, mostrar sus tableros
        this.showCompanyDashboards(entity);
    }

    /**
     * Carga las empresas desde la base de datos
     * @returns {Promise<Array>} - Promesa que se resuelve con la lista de empresas
     */
    loadEntities() {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores pueden gestionar empresas
            if (currentUser.tipo !== 'admin') {
                console.warn('No tiene permisos para gestionar empresas. Se mostrará una tabla vacía.');
                showNotification('No tiene permisos para gestionar empresas', 'warning');
                resolve([]);
                return;
            }

            // Optimización: Si no es de la empresa 1, solo necesitamos cargar su propia empresa
            if (currentCompanyId !== 1) {
                // Los administradores que no son de la empresa 1 solo pueden ver su empresa
                dbManager.getCompany(currentCompanyId)
                    .then(company => {
                        // Devolver un array con la empresa o un array vacío si no existe
                        resolve(company ? [company] : []);
                    })
                    .catch(error => {
                        console.error('Error al cargar empresa:', error);
                        showNotification(`Error al cargar empresa: ${error.message}`, 'error');
                        resolve([]);
                    });
            } else {
                // Los administradores de la empresa 1 pueden ver todas las empresas
                dbManager.getAllCompanies()
                    .then(companies => {
                        // Asegurarse de que siempre devuelve un array, incluso si está vacío
                        resolve(companies || []);
                    })
                    .catch(error => {
                        console.error('Error al cargar empresas:', error);
                        showNotification(`Error al cargar empresas: ${error.message}`, 'error');
                        resolve([]);
                    });
            }
        });
    }

    /**
     * Guarda una empresa en la base de datos
     * @param {Object} companyData - Datos de la empresa a guardar
     * @returns {Promise} - Promesa que se resuelve cuando la empresa se ha guardado
     */
    saveEntity(companyData) {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores pueden gestionar empresas
            if (currentUser.tipo !== 'admin') {
                reject(new Error('No tiene permisos para realizar esta acción'));
                return;
            }

            // Los administradores que no son de la empresa 1 solo pueden gestionar su propia empresa
            if (currentCompanyId !== 1 && companyData.id !== currentCompanyId) {
                reject(new Error('Solo puede gestionar su propia empresa'));
                return;
            }

            // Si es una nueva empresa, asignar ID (solo para administradores de empresa 1)
            if (!companyData.id) {
                // Solo los administradores de la empresa 1 pueden crear nuevas empresas
                if (currentCompanyId !== 1) {
                    reject(new Error('No tiene permisos para crear nuevas empresas'));
                    return;
                }

                // Obtener todas las empresas para determinar el siguiente ID
                dbManager.getAllCompanies()
                    .then(companies => {
                        const maxId = companies.reduce((max, company) => Math.max(max, company.id), 0);
                        companyData.id = maxId + 1;
                        return dbManager.saveCompany(companyData);
                    })
                    .then(() => resolve())
                    .catch(error => reject(error));
            } else {
                // No permitir desactivar la empresa 1
                if (companyData.id === 1 && !companyData.activo) {
                    reject(new Error('No se puede desactivar la empresa principal'));
                    return;
                }

                // Actualizar empresa existente
                dbManager.saveCompany(companyData)
                    .then(() => resolve())
                    .catch(error => reject(error));
            }
        });
    }

    /**
     * Elimina una empresa de la base de datos
     * @param {number} companyId - ID de la empresa a eliminar
     * @returns {Promise} - Promesa que se resuelve cuando la empresa se ha eliminado
     */
    deleteEntity(companyId) {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores pueden gestionar empresas
            if (currentUser.tipo !== 'admin') {
                reject(new Error('No tiene permisos para realizar esta acción'));
                return;
            }

            // Solo los administradores de la empresa 1 pueden eliminar empresas
            if (currentCompanyId !== 1) {
                reject(new Error('Solo los administradores de la empresa principal pueden eliminar empresas'));
                return;
            }

            // No permitir eliminar la empresa 1
            if (companyId === 1) {
                reject(new Error('No se puede eliminar la empresa principal'));
                return;
            }

            // Verificar si hay usuarios en la empresa
            dbManager.getUsers(companyId)
                .then(users => {
                    if (users.length > 0) {
                        reject(new Error('No se puede eliminar la empresa porque tiene usuarios asociados'));
                        return;
                    }

                    // Eliminar la empresa
                    // Nota: No hay una función directa para eliminar empresas en dbManager,
                    // así que tendríamos que implementarla o usar una alternativa

                    // Como alternativa, podemos desactivar la empresa
                    return dbManager.getCompany(companyId)
                        .then(company => {
                            if (!company) {
                                throw new Error('Empresa no encontrada');
                            }

                            company.activo = false;
                            return dbManager.saveCompany(company);
                        });
                })
                .then(() => {
                    resolve();
                })
                .catch(error => reject(error));
        });
    }

    /**
     * Genera datos de prueba para empresas
     * @returns {Promise} - Promesa que se resuelve cuando se han generado los datos
     */
    generateTestData() {
        return new Promise((resolve, reject) => {
            // Validar permisos
            const currentUser = authManager.user;
            const currentCompanyId = authManager.companyId;

            // Solo los administradores pueden gestionar empresas
            if (currentUser.tipo !== 'admin') {
                showNotification('No tiene permisos para realizar esta acción', 'error');
                reject(new Error('No tiene permisos para realizar esta acción'));
                return;
            }

            // Solo los administradores de la empresa 1 pueden generar datos de prueba
            if (currentCompanyId !== 1) {
                showNotification('Solo los administradores de la empresa principal pueden generar datos de prueba', 'error');
                reject(new Error('Solo los administradores de la empresa principal pueden generar datos de prueba'));
                return;
            }

            // Obtener todas las empresas para determinar el siguiente ID
            dbManager.getAllCompanies()
                .then(companies => {
                    const maxId = companies.reduce((max, company) => Math.max(max, company.id), 0);
                    let nextId = maxId + 1;

                    // Datos de prueba para empresas
                    const testCompanies = [{
                            id: nextId++,
                            nombre: 'Tecnología Avanzada S.L.',
                            direccion: 'Calle Innovación, 123',
                            ciudad: 'Madrid',
                            telefono: '*********',
                            email: '<EMAIL>',
                            nombreContacto: 'Carlos Rodríguez',
                            activo: true
                        },
                        {
                            id: nextId++,
                            nombre: 'Consultores Asociados',
                            direccion: 'Avenida Consultoría, 45',
                            ciudad: 'Barcelona',
                            telefono: '*********',
                            email: '<EMAIL>',
                            nombreContacto: 'Ana Martínez',
                            activo: true
                        },
                        {
                            id: nextId++,
                            nombre: 'Distribuciones Rápidas',
                            direccion: 'Polígono Industrial Norte, 78',
                            ciudad: 'Valencia',
                            telefono: '963456789',
                            email: '<EMAIL>',
                            nombreContacto: 'Pedro Sánchez',
                            activo: true
                        },
                        {
                            id: nextId++,
                            nombre: 'Manufacturas del Sur',
                            direccion: 'Calle Industria, 56',
                            ciudad: 'Sevilla',
                            telefono: '954321098',
                            email: '<EMAIL>',
                            nombreContacto: 'Laura Gómez',
                            activo: true
                        },
                        {
                            id: nextId++,
                            nombre: 'Servicios Financieros',
                            direccion: 'Plaza del Dinero, 1',
                            ciudad: 'Bilbao',
                            telefono: '944567890',
                            email: '<EMAIL>',
                            nombreContacto: 'Javier López',
                            activo: true
                        },
                        {
                            id: nextId++,
                            nombre: 'Construcciones Modernas',
                            direccion: 'Avenida Arquitectura, 89',
                            ciudad: 'Zaragoza',
                            telefono: '976543210',
                            email: '<EMAIL>',
                            nombreContacto: 'María Fernández',
                            activo: false
                        },
                        {
                            id: nextId++,
                            nombre: 'Transportes Rápidos',
                            direccion: 'Carretera Principal, 45',
                            ciudad: 'Málaga',
                            telefono: '952345678',
                            email: '<EMAIL>',
                            nombreContacto: 'Roberto Díaz',
                            activo: true
                        },
                        {
                            id: nextId++,
                            nombre: 'Alimentación Natural',
                            direccion: 'Calle Orgánica, 23',
                            ciudad: 'Murcia',
                            telefono: '*********',
                            email: '<EMAIL>',
                            nombreContacto: 'Elena Torres',
                            activo: true
                        }
                    ];

                    // Guardar todas las empresas de prueba
                    const savePromises = testCompanies.map(company => dbManager.saveCompany(company));

                    return Promise.all(savePromises);
                })
                .then(() => {
                    showNotification('Datos de prueba generados correctamente', 'success');

                    // Recargar la lista de empresas
                    return this.loadEntities();
                })
                .then(companies => {
                    // Actualizar la tabla
                    if (window.entityManager) {
                        window.entityManager.entities = companies;
                        window.entityManager.applyFilter();
                        window.entityManager.renderEntityTable();
                    }

                    resolve();
                })
                .catch(error => {
                    console.error('Error al generar datos de prueba:', error);
                    showNotification('Error al generar datos de prueba', 'error');
                    reject(error);
                });
        });
    }
}

// Registrar el manejador de empresas en el gestor de entidades
app['entityManager'].registerEntityHandler('companies', new CompanyEntityHandler());